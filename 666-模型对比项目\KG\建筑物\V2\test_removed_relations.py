"""
测试脚本：验证 locateAt、cateOf 和 belongTo 关系已被成功移除
"""

import sys
import os

def test_removed_relations():
    """测试移除的关系是否不再生成"""
    
    # 读取修改后的代码文件
    script_path = "enhanced_kg_with_building_v4.py"
    
    if not os.path.exists(script_path):
        print(f"❌ 文件不存在: {script_path}")
        return False
    
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键修改点
    checks = [
        {
            'name': 'locateAt关系生成被注释',
            'pattern': '# print("3. locateAt - POI位置关系...")',
            'found': False
        },
        {
            'name': 'cateOf关系生成被注释', 
            'pattern': '# print("1. cateOf - POI类别归属...")',
            'found': False
        },
        {
            'name': 'belongTo关系生成被注释',
            'pattern': '# print("2. belongTo - POI商圈归属...")',
            'found': False
        },
        {
            'name': '关系总数更新为16种',
            'pattern': '最终关系类型（16种）',
            'found': False
        },
        {
            'name': 'locateAt标记为已移除',
            'pattern': '### 1.4 locateAt 关系 ❌**已移除**',
            'found': False
        },
        {
            'name': 'cateOf标记为已移除',
            'pattern': '### 4.1 cateOf 关系 ❌**已移除**',
            'found': False
        },
        {
            'name': 'belongTo标记为已移除',
            'pattern': '### 4.2 belongTo 关系 ❌**已移除**',
            'found': False
        },
        {
            'name': '分类关系章节标记为全部移除',
            'pattern': '## 4. 分类关系 (Categorical Relations) - 0种 ❌**已全部移除**',
            'found': False
        },
        {
            'name': '连通性分析中移除所有分类关系',
            'pattern': "'categorical': 0,  # 移除 cateOf 和 belongTo",
            'found': False
        },
        {
            'name': '移除关系列表包含belongTo',
            'pattern': '10. **belongTo**: 移除POI商圈归属关系 ❌**新移除**',
            'found': False
        }
    ]
    
    # 执行检查
    for check in checks:
        if check['pattern'] in content:
            check['found'] = True
    
    # 输出检查结果
    print("🔍 关系移除验证结果:")
    print("="*60)
    
    all_passed = True
    for check in checks:
        status = "✅ 通过" if check['found'] else "❌ 失败"
        print(f"{status} {check['name']}")
        if not check['found']:
            all_passed = False
    
    print("="*60)
    
    if all_passed:
        print("🎉 所有检查通过！locateAt、cateOf 和 belongTo 关系已成功移除")
        
        # 额外检查：确保没有遗漏的生成代码
        print("\n🔍 额外检查：搜索可能遗漏的关系生成代码...")
        
        # 搜索可能的遗漏
        potential_issues = []
        
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            # 检查是否有未注释的关系生成
            for relation in ['locateAt', 'cateOf', 'belongTo']:
                if relation in line and 'triples.append' in line and not line.strip().startswith('#'):
                    potential_issues.append(f"第{i}行: 可能的{relation}生成代码未注释")
        
        if potential_issues:
            print("⚠️ 发现潜在问题:")
            for issue in potential_issues:
                print(f"  {issue}")
        else:
            print("✅ 未发现遗漏的关系生成代码")
        
        return True
    else:
        print("❌ 部分检查失败，请检查修改是否完整")
        return False

def show_relation_summary():
    """显示修改后的关系总结"""
    print("\n📊 修改后的知识图谱关系总结:")
    print("="*60)
    print("总关系类型: 16种 (原19种，移除3种)")
    print()
    print("保留的关系类型:")
    print("  空间关系(5种): borderBy, nearBy, connectedTo, withinRegion, adjacentToLand")
    print("  功能相似性(3种): similarFunction, highConvenience, morphologySimilar")
    print("  移动性(2种): flowTransition, densityInfluences")
    print("  分类(0种): [全部移除]")
    print("  服务(1种): provideService")
    print("  建筑物(4种): belongsToBuilding, belongsToLand, hasFunctionBuilding, hasMorphology")
    print("  土地利用(1种): hasLandUse")
    print()
    print("❌ 已移除的关系类型:")
    print("  locateAt: POI位置关系")
    print("  cateOf: POI类别归属关系")
    print("  belongTo: POI商圈归属关系")
    print()
    print("🎯 移除原因:")
    print("  - 完全移除POI相关关系")
    print("  - 大幅简化关系结构")
    print("  - 减少冗余连接")
    print("  - 专注于核心空间、功能、形态关系")

if __name__ == "__main__":
    print("🚀 开始验证 locateAt、cateOf 和 belongTo 关系移除...")
    
    success = test_removed_relations()
    
    if success:
        show_relation_summary()
        print("\n✅ 验证完成：所有POI相关关系移除成功！")
    else:
        print("\n❌ 验证失败：请检查代码修改")
    
    print("\n💡 提示：运行修改后的知识图谱生成器时，")
    print("   应该看到以下提示信息：")
    print("   - 'locateAt - POI位置关系... [已停用]'")
    print("   - 'cateOf - POI类别归属... [已停用]'")
    print("   - 'belongTo - POI商圈归属... [已停用]'")
