"""
知识图谱分析器
分析三元组结构，统计实体类型，特别关注功能和形态类别
"""

import pandas as pd
import numpy as np
from collections import defaultdict, Counter
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import re

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class KnowledgeGraphAnalyzer:
    def __init__(self, kg_file_path, output_dir="./kg_analysis_results"):
        """
        初始化知识图谱分析器
        
        Args:
            kg_file_path: 知识图谱文件路径（三元组格式）
            output_dir: 分析结果输出目录
        """
        self.kg_file_path = kg_file_path
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 存储分析结果
        self.triples = []
        self.entities = set()
        self.relations = set()
        self.entity_types = defaultdict(set)
        self.relation_stats = defaultdict(int)
        self.entity_stats = defaultdict(int)
        
        # 实体类型模式
        self.entity_patterns = {
            'Region': r'^Region_',
            'Land': r'^Land_', 
            'Building': r'^Building_',
            'POI': r'^POI_',
            'BusinessCircle': r'^BC_',
            'Category': r'^Cate_',
            'Function': r'^Func_',
            'Morphology': r'^Morph_'
        }
        
        # 功能类型映射
        self.function_types = {
            'Func_Residential': '住宅',
            'Func_Commercial': '商业',
            'Func_Office': '办公',
            'Func_Industrial': '工业',
            'Func_Public': '公共',
            'Func_Education': '教育',
            'Func_Medical': '医疗',
            'Func_Cultural': '文化',
            'Func_Sports': '体育',
            'Func_Transport': '交通',
            'Func_Other': '其他'
        }
        
        # 形态类型映射
        self.morphology_types = {
            'Morph_LowRiseLowDensity': '低层低密度',
            'Morph_LowRiseMidDensity': '低层中密度', 
            'Morph_LowRiseHighDensity': '低层高密度',
            'Morph_MidRiseLowDensity': '中层低密度',
            'Morph_MidRiseMidDensity': '中层中密度',
            'Morph_MidRiseHighDensity': '中层高密度',
            'Morph_HighRiseLowDensity': '高层低密度',
            'Morph_HighRiseMidDensity': '高层中密度',
            'Morph_HighRiseHighDensity': '高层高密度',
            'Morph_Vacant': '空地'
        }
        
    def load_knowledge_graph(self):
        """加载知识图谱三元组"""
        print("🔍 加载知识图谱三元组...")
        
        try:
            with open(self.kg_file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                        
                    parts = line.split('\t')
                    if len(parts) != 3:
                        print(f"⚠️ 第{line_num}行格式错误: {line}")
                        continue
                    
                    head, relation, tail = parts
                    self.triples.append((head, relation, tail))
                    self.entities.add(head)
                    self.entities.add(tail)
                    self.relations.add(relation)
                    
            print(f"✅ 成功加载 {len(self.triples):,} 个三元组")
            print(f"   实体数量: {len(self.entities):,}")
            print(f"   关系数量: {len(self.relations):,}")
            
        except FileNotFoundError:
            print(f"❌ 文件未找到: {self.kg_file_path}")
            return False
        except Exception as e:
            print(f"❌ 加载失败: {e}")
            return False
            
        return True
    
    def classify_entities(self):
        """对实体进行分类"""
        print("\n📊 分类实体类型...")
        
        for entity in self.entities:
            classified = False
            for entity_type, pattern in self.entity_patterns.items():
                if re.match(pattern, entity):
                    self.entity_types[entity_type].add(entity)
                    self.entity_stats[entity_type] += 1
                    classified = True
                    break
            
            if not classified:
                self.entity_types['Unknown'].add(entity)
                self.entity_stats['Unknown'] += 1
        
        # 打印实体类型统计
        print("\n实体类型统计:")
        for entity_type, count in sorted(self.entity_stats.items(), 
                                       key=lambda x: x[1], reverse=True):
            percentage = count / len(self.entities) * 100
            print(f"  {entity_type:<15}: {count:>8,} ({percentage:>5.1f}%)")
    
    def analyze_relations(self):
        """分析关系统计"""
        print("\n🔗 分析关系统计...")
        
        # 统计关系频次
        for head, relation, tail in self.triples:
            self.relation_stats[relation] += 1
        
        # 按频次排序
        sorted_relations = sorted(self.relation_stats.items(), 
                                key=lambda x: x[1], reverse=True)
        
        print("\n关系类型统计:")
        for relation, count in sorted_relations:
            percentage = count / len(self.triples) * 100
            print(f"  {relation:<25}: {count:>7,} ({percentage:>5.1f}%)")
        
        return sorted_relations
    
    def analyze_tail_entities(self):
        """分析尾实体类型分布"""
        print("\n🎯 分析尾实体类型分布...")
        
        tail_entity_stats = defaultdict(lambda: defaultdict(int))
        tail_by_relation = defaultdict(lambda: defaultdict(int))
        
        for head, relation, tail in self.triples:
            # 确定尾实体类型
            tail_type = 'Unknown'
            for entity_type, pattern in self.entity_patterns.items():
                if re.match(pattern, tail):
                    tail_type = entity_type
                    break
            
            tail_entity_stats[tail_type][tail] += 1
            tail_by_relation[relation][tail_type] += 1
        
        # 打印尾实体类型统计
        print("\n尾实体类型分布:")
        for entity_type, entities in tail_entity_stats.items():
            unique_count = len(entities)
            total_count = sum(entities.values())
            print(f"  {entity_type:<15}: {unique_count:>6} 种 (出现 {total_count:>7,} 次)")
        
        # 按关系分析尾实体
        print("\n各关系的尾实体类型:")
        for relation in sorted(tail_by_relation.keys()):
            print(f"\n  📌 {relation}:")
            for tail_type, count in sorted(tail_by_relation[relation].items(), 
                                         key=lambda x: x[1], reverse=True):
                percentage = count / self.relation_stats[relation] * 100
                print(f"    → {tail_type:<15}: {count:>6,} ({percentage:>5.1f}%)")
        
        return tail_entity_stats, tail_by_relation
    
    def analyze_function_morphology_details(self):
        """详细分析功能和形态类别"""
        print("\n🏗️ 详细分析功能和形态类别...")
        
        # 功能类别详细分析
        function_entities = self.entity_types.get('Function', set())
        morphology_entities = self.entity_types.get('Morphology', set())
        
        print(f"\n功能类别详情 (共 {len(function_entities)} 种):")
        if function_entities:
            function_usage = defaultdict(int)
            for head, relation, tail in self.triples:
                if tail in function_entities:
                    function_usage[tail] += 1
            
            # 按使用频次排序
            sorted_functions = sorted(function_usage.items(), 
                                    key=lambda x: x[1], reverse=True)
            
            for func_id, count in sorted_functions:
                func_name = self.function_types.get(func_id, func_id)
                percentage = count / sum(function_usage.values()) * 100 if function_usage.values() else 0
                print(f"  {func_id:<25} ({func_name:<8}): {count:>5} 次 ({percentage:>5.1f}%)")
        else:
            print("  未发现功能类别实体")
        
        print(f"\n形态类别详情 (共 {len(morphology_entities)} 种):")
        if morphology_entities:
            morphology_usage = defaultdict(int)
            for head, relation, tail in self.triples:
                if tail in morphology_entities:
                    morphology_usage[tail] += 1
            
            # 按使用频次排序
            sorted_morphologies = sorted(morphology_usage.items(), 
                                       key=lambda x: x[1], reverse=True)
            
            for morph_id, count in sorted_morphologies:
                morph_name = self.morphology_types.get(morph_id, morph_id)
                percentage = count / sum(morphology_usage.values()) * 100 if morphology_usage.values() else 0
                print(f"  {morph_id:<30} ({morph_name:<12}): {count:>5} 次 ({percentage:>5.1f}%)")
        else:
            print("  未发现形态类别实体")
        
        return function_entities, morphology_entities
    
    def generate_sample_triples(self, sample_size=50):
        """生成三元组样本展示"""
        print(f"\n📋 三元组样本展示 (前 {sample_size} 个):")
        
        sample_triples = self.triples[:sample_size]
        
        print("\n头实体\t\t\t关系\t\t\t尾实体")
        print("-" * 80)
        
        for i, (head, relation, tail) in enumerate(sample_triples, 1):
            # 截断过长的实体名
            head_display = head[:20] + "..." if len(head) > 20 else head
            tail_display = tail[:20] + "..." if len(tail) > 20 else tail
            
            print(f"{head_display:<25}\t{relation:<20}\t{tail_display}")
            
            # 每10行显示一个分隔符
            if i % 10 == 0 and i < sample_size:
                print("-" * 80)
        
        return sample_triples
    
    def create_visualizations(self):
        """创建可视化图表"""
        print("\n📈 生成可视化图表...")
        
        # 1. 实体类型分布饼图
        plt.figure(figsize=(12, 8))
        
        plt.subplot(2, 2, 1)
        entity_counts = list(self.entity_stats.values())
        entity_labels = list(self.entity_stats.keys())
        colors = plt.cm.Set3(np.linspace(0, 1, len(entity_labels)))
        
        plt.pie(entity_counts, labels=entity_labels, autopct='%1.1f%%', 
                colors=colors, startangle=90)
        plt.title('实体类型分布', fontsize=14, fontweight='bold')
        
        # 2. 关系类型频次柱状图
        plt.subplot(2, 2, 2)
        relations = list(self.relation_stats.keys())[:10]  # 前10个关系
        counts = [self.relation_stats[r] for r in relations]
        
        plt.bar(range(len(relations)), counts, color='skyblue', alpha=0.7)
        plt.xticks(range(len(relations)), relations, rotation=45, ha='right')
        plt.ylabel('频次')
        plt.title('关系类型频次 (Top 10)', fontsize=14, fontweight='bold')
        plt.tight_layout()
        
        # 3. 功能类别分布
        function_entities = self.entity_types.get('Function', set())
        if function_entities:
            plt.subplot(2, 2, 3)
            function_usage = defaultdict(int)
            for head, relation, tail in self.triples:
                if tail in function_entities:
                    function_usage[tail] += 1
            
            if function_usage:
                func_names = [self.function_types.get(f, f.replace('Func_', '')) 
                             for f in function_usage.keys()]
                func_counts = list(function_usage.values())
                
                plt.bar(range(len(func_names)), func_counts, color='lightgreen', alpha=0.7)
                plt.xticks(range(len(func_names)), func_names, rotation=45, ha='right')
                plt.ylabel('使用次数')
                plt.title('建筑功能类别分布', fontsize=14, fontweight='bold')
        
        # 4. 形态类别分布
        morphology_entities = self.entity_types.get('Morphology', set())
        if morphology_entities:
            plt.subplot(2, 2, 4)
            morphology_usage = defaultdict(int)
            for head, relation, tail in self.triples:
                if tail in morphology_entities:
                    morphology_usage[tail] += 1
            
            if morphology_usage:
                morph_names = [self.morphology_types.get(m, m.replace('Morph_', '')) 
                              for m in morphology_usage.keys()]
                morph_counts = list(morphology_usage.values())
                
                plt.bar(range(len(morph_names)), morph_counts, color='lightcoral', alpha=0.7)
                plt.xticks(range(len(morph_names)), morph_names, rotation=45, ha='right')
                plt.ylabel('使用次数')
                plt.title('建筑形态类别分布', fontsize=14, fontweight='bold')
        
        plt.tight_layout()
        
        # 保存图表
        viz_path = self.output_dir / "kg_analysis_visualization.png"
        plt.savefig(viz_path, dpi=300, bbox_inches='tight')
        print(f"✅ 可视化图表已保存: {viz_path}")
        
        plt.show()
    
    def export_analysis_results(self):
        """导出分析结果"""
        print("\n💾 导出分析结果...")
        
        # 1. 基本统计信息
        basic_stats = {
            'total_triples': len(self.triples),
            'total_entities': len(self.entities),
            'total_relations': len(self.relations),
            'entity_type_counts': dict(self.entity_stats),
            'relation_counts': dict(self.relation_stats)
        }
        
        with open(self.output_dir / "basic_statistics.json", 'w', encoding='utf-8') as f:
            json.dump(basic_stats, f, ensure_ascii=False, indent=2)
        
        # 2. 实体类型详情
        entity_details = {}
        for entity_type, entities in self.entity_types.items():
            entity_details[entity_type] = {
                'count': len(entities),
                'entities': sorted(list(entities))[:100]  # 最多保存100个示例
            }
        
        with open(self.output_dir / "entity_details.json", 'w', encoding='utf-8') as f:
            json.dump(entity_details, f, ensure_ascii=False, indent=2)
        
        # 3. 功能和形态详情
        function_morphology_details = {
            'functions': {
                'mapping': self.function_types,
                'entities': sorted(list(self.entity_types.get('Function', set())))
            },
            'morphologies': {
                'mapping': self.morphology_types,
                'entities': sorted(list(self.entity_types.get('Morphology', set())))
            }
        }
        
        with open(self.output_dir / "function_morphology_details.json", 'w', encoding='utf-8') as f:
            json.dump(function_morphology_details, f, ensure_ascii=False, indent=2)
        
        # 4. 三元组样本
        sample_triples = self.triples[:1000]  # 前1000个三元组样本
        triples_df = pd.DataFrame(sample_triples, columns=['头实体', '关系', '尾实体'])
        triples_df.to_csv(self.output_dir / "sample_triples.csv", 
                         index=False, encoding='utf-8-sig')
        
        # 5. 尾实体统计
        tail_stats = defaultdict(lambda: defaultdict(int))
        for head, relation, tail in self.triples:
            tail_type = 'Unknown'
            for entity_type, pattern in self.entity_patterns.items():
                if re.match(pattern, tail):
                    tail_type = entity_type
                    break
            tail_stats[relation][tail_type] += 1
        
        # 转换为DataFrame
        tail_stats_data = []
        for relation, type_counts in tail_stats.items():
            for tail_type, count in type_counts.items():
                tail_stats_data.append({
                    '关系': relation,
                    '尾实体类型': tail_type,
                    '数量': count,
                    '占该关系比例': f"{count/self.relation_stats[relation]*100:.1f}%"
                })
        
        tail_stats_df = pd.DataFrame(tail_stats_data)
        tail_stats_df.to_csv(self.output_dir / "tail_entity_statistics.csv", 
                           index=False, encoding='utf-8-sig')
        
        print(f"✅ 分析结果已导出到: {self.output_dir}")
        print("   📄 basic_statistics.json - 基本统计信息")
        print("   📄 entity_details.json - 实体类型详情")
        print("   📄 function_morphology_details.json - 功能形态详情")
        print("   📄 sample_triples.csv - 三元组样本")
        print("   📄 tail_entity_statistics.csv - 尾实体统计")
    
    def run_full_analysis(self):
        """运行完整分析"""
        print("🚀 开始知识图谱完整分析")
        print("=" * 60)
        
        # 1. 加载数据
        if not self.load_knowledge_graph():
            return False
        
        # 2. 分类实体
        self.classify_entities()
        
        # 3. 分析关系
        self.analyze_relations()
        
        # 4. 分析尾实体
        self.analyze_tail_entities()
        
        # 5. 详细分析功能和形态
        self.analyze_function_morphology_details() 
        
        # 6. 生成样本展示
        self.generate_sample_triples()
        
        # 7. 创建可视化
        try:
            self.create_visualizations()
        except Exception as e:
            print(f"⚠️ 可视化生成失败: {e}")
        
        # 8. 导出结果
        self.export_analysis_results()
        
        print("\n" + "=" * 60)
        print("🎉 知识图谱分析完成！")
        
        return True


def main():
    """主函数示例"""
    # 配置文件路径
    kg_file_path = r"D:\研二\能耗估算\666-模型对比项目\KG\建筑物\kg_with_building_fixed.txt"
    output_dir = r"D:\研二\能耗估算\666-模型对比项目\KG\建筑物\analysis_results"
    
    # 创建分析器
    analyzer = KnowledgeGraphAnalyzer(kg_file_path, output_dir)
    
    # 运行分析
    success = analyzer.run_full_analysis()
    
    if success:
        print("\n📋 分析总结:")
        print(f"   三元组总数: {len(analyzer.triples):,}")
        print(f"   实体总数: {len(analyzer.entities):,}")
        print(f"   关系类型数: {len(analyzer.relations):,}")
        print(f"   实体类型数: {len(analyzer.entity_stats):,}")
        
        # 特别展示功能和形态类别
        function_count = len(analyzer.entity_types.get('Function', set()))
        morphology_count = len(analyzer.entity_types.get('Morphology', set()))
        
        print(f"\n🎯 重点关注:")
        print(f"   功能类别数: {function_count} 种")
        print(f"   形态类别数: {morphology_count} 种")
        
        if function_count > 0:
            print(f"   功能类别: {', '.join(sorted(analyzer.entity_types['Function']))}")
        
        if morphology_count > 0:
            print(f"   形态类别: {', '.join(sorted(analyzer.entity_types['Morphology']))}")


if __name__ == "__main__":
    main()