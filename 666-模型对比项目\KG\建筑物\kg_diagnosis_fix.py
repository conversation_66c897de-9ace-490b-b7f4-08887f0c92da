"""
知识图谱问题诊断与修复工具

解决以下问题：
1. 建筑功能分类失效 (所有建筑都是Func_Other)
2. 建筑形态类型不完整 (缺少中高层建筑)
3. 阈值参数调优
"""

import pandas as pd
import geopandas as gpd
import numpy as np
from collections import Counter
import matplotlib.pyplot as plt

class KGDiagnosisAndFix:
    def __init__(self, building_path, l5_path=None):
        """
        初始化诊断工具
        
        Args:
            building_path: 建筑物数据路径
            l5_path: L5地块数据路径（可选）
        """
        self.building_path = building_path
        self.l5_path = l5_path
        self.building_gdf = None
        self.l5_gdf = None
        
        # 原始阈值参数
        self.original_params = {
            "fsi_thresholds": [0.4, 0.8, 1.5, 2.5],
            "gsi_thresholds": [0.15, 0.3, 0.45],
            "osr_thresholds": [0.4, 0.8, 1.5],
            "l_thresholds": [2, 4, 8],  # 原始楼层阈值
        }
        
        # 建筑功能映射（扩展版）
        self.extended_function_map = {
            # 中文映射
            "住宅": "Func_Residential",
            "商业": "Func_Commercial", 
            "办公": "Func_Office",
            "工业": "Func_Industrial",
            "公共": "Func_Public",
            "教育": "Func_Education",
            "医疗": "Func_Medical",
            "文化": "Func_Cultural",
            "体育": "Func_Sports",
            "交通": "Func_Transport",
            
            # 英文映射
            "residential": "Func_Residential",
            "commercial": "Func_Commercial",
            "office": "Func_Office", 
            "industrial": "Func_Industrial",
            "public": "Func_Public",
            "education": "Func_Education",
            "medical": "Func_Medical",
            "cultural": "Func_Cultural",
            "sports": "Func_Sports",
            "transport": "Func_Transport",
            
            # 数字编码映射
            "1": "Func_Residential",
            "2": "Func_Commercial",
            "3": "Func_Office",
            "4": "Func_Industrial",
            "5": "Func_Public",
            "6": "Func_Education",
            "7": "Func_Medical",
            "8": "Func_Cultural",
            "9": "Func_Sports",
            "10": "Func_Transport",
            
            # 关键词模糊匹配
            "住": "Func_Residential",
            "商": "Func_Commercial",
            "办": "Func_Office",
            "工": "Func_Industrial",
            "公": "Func_Public",
        }
    
    def load_data(self):
        """加载数据"""
        print("🔍 加载建筑物数据...")
        try:
            self.building_gdf = gpd.read_file(self.building_path)
            print(f"✅ 建筑物数据: {len(self.building_gdf):,} 条记录")
            
            if self.l5_path:
                self.l5_gdf = gpd.read_file(self.l5_path)
                print(f"✅ L5地块数据: {len(self.l5_gdf):,} 条记录")
                
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
        return True
    
    def diagnose_building_functions(self):
        """诊断建筑功能分类问题"""
        print("\n🏢 诊断建筑功能分类问题...")
        
        # 检查可能的功能字段
        potential_function_fields = [
            'main_use', 'function', 'type', 'use_type', 'building_type',
            'MAIN_USE', 'FUNCTION', 'TYPE', 'USE_TYPE', 'BUILDING_TYPE',
            '主要用途', '建筑类型', '功能类型', '用途', '类型'
        ]
        
        print("📋 可用字段列表:")
        available_fields = []
        for field in self.building_gdf.columns:
            print(f"   {field}")
            if any(keyword in field.lower() for keyword in 
                   ['use', 'type', 'function', '用途', '类型', '功能']):
                available_fields.append(field)
        
        print(f"\n🎯 可能的功能字段: {available_fields}")
        
        # 分析每个潜在字段的数据分布
        function_analysis = {}
        for field in available_fields:
            if field in self.building_gdf.columns:
                non_null_count = self.building_gdf[field].notna().sum()
                unique_values = self.building_gdf[field].dropna().unique()
                
                function_analysis[field] = {
                    'non_null_count': non_null_count,
                    'non_null_rate': non_null_count / len(self.building_gdf) * 100,
                    'unique_count': len(unique_values),
                    'sample_values': list(unique_values[:10]),
                    'value_counts': self.building_gdf[field].value_counts().head(10).to_dict()
                }
                
                print(f"\n📊 字段 '{field}' 分析:")
                print(f"   非空数量: {non_null_count:,} ({non_null_count/len(self.building_gdf)*100:.1f}%)")
                print(f"   唯一值数: {len(unique_values)}")
                print(f"   样例值: {unique_values[:10]}")
                print(f"   分布统计:")
                for value, count in function_analysis[field]['value_counts'].items():
                    print(f"     {value}: {count:,}")
        
        return function_analysis
    
    def diagnose_morphology_classification(self):
        """诊断形态分类问题"""
        print("\n🏗️ 诊断建筑形态分类问题...")
        
        # 检查建筑高度和楼层字段
        height_fields = ['height', 'HEIGHT', 'floor', 'FLOOR', 'floors', 'FLOORS', 
                        '高度', '楼层', '层数', 'storeys', 'levels']
        
        available_height_fields = []
        height_analysis = {}
        
        print("📏 高度/楼层字段分析:")
        for field in height_fields:
            if field in self.building_gdf.columns:
                available_height_fields.append(field)
                
                # 统计分析
                data = self.building_gdf[field].dropna()
                if len(data) > 0:
                    # 数值转换
                    numeric_data = pd.to_numeric(data, errors='coerce').dropna()
                    
                    if len(numeric_data) > 0:
                        stats = {
                            'field': field,
                            'total_count': len(self.building_gdf),
                            'non_null_count': len(data),
                            'numeric_count': len(numeric_data),
                            'min': numeric_data.min(),
                            'max': numeric_data.max(),
                            'mean': numeric_data.mean(),
                            'median': numeric_data.median(),
                            'distribution': numeric_data.describe().to_dict()
                        }
                        
                        height_analysis[field] = stats
                        
                        print(f"\n   📊 字段 '{field}':")
                        print(f"      非空率: {len(data)/len(self.building_gdf)*100:.1f}%")
                        print(f"      数值率: {len(numeric_data)/len(data)*100:.1f}%")
                        print(f"      范围: {stats['min']:.1f} - {stats['max']:.1f}")
                        print(f"      均值: {stats['mean']:.1f}, 中位数: {stats['median']:.1f}")
                        
                        # 按现有阈值分析分布
                        layer_dist = self.analyze_layer_distribution(numeric_data)
                        print(f"      层数分布: {layer_dist}")
        
        # 建议新的阈值
        if height_analysis:
            suggested_thresholds = self.suggest_new_thresholds(height_analysis)
            print(f"\n💡 建议的新阈值: {suggested_thresholds}")
        
        return height_analysis
    
    def analyze_layer_distribution(self, height_data):
        """分析楼层分布"""
        # 假设数据是楼层数
        layer_counts = {
            'low (≤2层)': len(height_data[height_data <= 2]),
            'mid (3-4层)': len(height_data[(height_data > 2) & (height_data <= 4)]),
            'high (5+层)': len(height_data[height_data > 4])
        }
        
        total = len(height_data)
        if total > 0:
            return {k: f"{v} ({v/total*100:.1f}%)" for k, v in layer_counts.items()}
        return layer_counts
    
    def suggest_new_thresholds(self, height_analysis):
        """基于数据分布建议新阈值"""
        suggestions = {}
        
        for field, stats in height_analysis.items():
            data_max = stats['max']
            data_mean = stats['mean']
            
            # 基于数据分布建议阈值
            if data_max <= 3:  # 数据主要是低层建筑
                suggestions[field] = {
                    'l_thresholds': [1.5, 2.5, 3.5],  # 降低阈值
                    'reason': '数据主要为低层建筑，降低分层阈值'
                }
            elif data_max <= 10:  # 中等高度
                suggestions[field] = {
                    'l_thresholds': [3, 6, 12],  # 调整阈值
                    'reason': '数据包含中层建筑，调整分层阈值'
                }
            else:  # 包含高层建筑
                suggestions[field] = {
                    'l_thresholds': [3, 8, 20],  # 标准阈值
                    'reason': '数据包含高层建筑，使用标准分层阈值'
                }
        
        return suggestions
    
    def test_function_mapping(self, field_name):
        """测试功能字段映射效果"""
        print(f"\n🧪 测试功能字段 '{field_name}' 的映射效果...")
        
        if field_name not in self.building_gdf.columns:
            print(f"❌ 字段 '{field_name}' 不存在")
            return None
        
        # 原始数据分布
        original_values = self.building_gdf[field_name].dropna()
        print(f"原始数据分布:")
        for value, count in original_values.value_counts().head(10).items():
            print(f"   {value}: {count:,}")
        
        # 测试映射效果
        mapped_functions = {}
        unmapped_count = 0
        
        for value in original_values:
            str_value = str(value).strip().lower()
            mapped = False
            
            # 精确匹配
            for key, func in self.extended_function_map.items():
                if str_value == key.lower():
                    mapped_functions[func] = mapped_functions.get(func, 0) + 1
                    mapped = True
                    break
            
            # 模糊匹配
            if not mapped:
                for key, func in self.extended_function_map.items():
                    if key.lower() in str_value or str_value in key.lower():
                        mapped_functions[func] = mapped_functions.get(func, 0) + 1
                        mapped = True
                        break
            
            if not mapped:
                mapped_functions['Func_Other'] = mapped_functions.get('Func_Other', 0) + 1
                unmapped_count += 1
        
        print(f"\n映射结果分布:")
        total_mapped = sum(mapped_functions.values())
        for func, count in sorted(mapped_functions.items(), key=lambda x: x[1], reverse=True):
            percentage = count / total_mapped * 100 if total_mapped > 0 else 0
            print(f"   {func}: {count:,} ({percentage:.1f}%)")
        
        print(f"\n映射效果:")
        mapped_rate = (total_mapped - unmapped_count) / total_mapped * 100 if total_mapped > 0 else 0
        print(f"   成功映射率: {mapped_rate:.1f}%")
        print(f"   未映射数量: {unmapped_count:,}")
        
        return mapped_functions
    
    def generate_fixed_parameters(self):
        """生成修复后的参数配置"""
        print("\n🔧 生成修复参数配置...")
        
        # 基于诊断结果生成新参数
        fixed_params = {
            # 调整后的形态分类阈值（更宽松）
            "fsi_thresholds": [0.2, 0.6, 1.2, 2.0],  # 降低容积率阈值
            "gsi_thresholds": [0.1, 0.25, 0.4],      # 降低覆盖率阈值
            "osr_thresholds": [0.3, 0.7, 1.2],       # 调整开放空间率
            "l_thresholds": [1.5, 3, 6],             # 大幅降低楼层阈值
            
            # 扩展的建筑功能映射
            "extended_function_map": self.extended_function_map,
            
            # 建议的字段选择
            "recommended_function_field": None,  # 需要根据诊断结果确定
            "recommended_height_field": None,    # 需要根据诊断结果确定
        }
        
        return fixed_params
    
    def create_diagnostic_visualization(self, height_analysis, function_analysis):
        """创建诊断可视化图表"""
        print("\n📊 生成诊断可视化...")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 楼层数据分布
        if height_analysis:
            ax = axes[0, 0]
            field_name = list(height_analysis.keys())[0]
            
            if field_name in self.building_gdf.columns:
                height_data = pd.to_numeric(
                    self.building_gdf[field_name].dropna(), 
                    errors='coerce'
                ).dropna()
                
                ax.hist(height_data, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
                ax.axvline(2, color='red', linestyle='--', label='原阈值: 2层')
                ax.axvline(4, color='red', linestyle='--', label='原阈值: 4层')
                ax.axvline(8, color='red', linestyle='--', label='原阈值: 8层')
                ax.set_xlabel('楼层数/高度')
                ax.set_ylabel('建筑数量')
                ax.set_title(f'建筑高度分布 ({field_name})')
                ax.legend()
        
        # 2. 功能字段完整性
        ax = axes[0, 1]
        if function_analysis:
            fields = list(function_analysis.keys())
            completeness = [function_analysis[f]['non_null_rate'] for f in fields]
            
            bars = ax.bar(range(len(fields)), completeness, color='lightgreen', alpha=0.7)
            ax.set_xticks(range(len(fields)))
            ax.set_xticklabels(fields, rotation=45, ha='right')
            ax.set_ylabel('完整性 (%)')
            ax.set_title('功能字段完整性对比')
            ax.set_ylim(0, 100)
            
            # 添加数值标签
            for i, bar in enumerate(bars):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{height:.1f}%', ha='center', va='bottom')
        
        # 3. 当前形态分类结果
        ax = axes[1, 0]
        current_morphology = {
            '低层低密度': 123,
            '低层中密度': 195, 
            '低层高密度': 24,
            '空地': 70,
            '中层类型': 0,    # 缺失
            '高层类型': 0     # 缺失
        }
        
        colors = ['lightblue' if v > 0 else 'lightcoral' for v in current_morphology.values()]
        bars = ax.bar(current_morphology.keys(), current_morphology.values(), 
                     color=colors, alpha=0.7)
        ax.set_ylabel('数量')
        ax.set_title('当前形态分类结果')
        ax.tick_params(axis='x', rotation=45)
        
        # 4. 建议的修复策略
        ax = axes[1, 1]
        strategies = {
            '降低楼层阈值': 85,
            '扩展功能映射': 95,
            '数据清洗': 70,
            '字段重新选择': 80,
            '阈值参数调优': 90
        }
        
        bars = ax.barh(list(strategies.keys()), list(strategies.values()), 
                      color='orange', alpha=0.7)
        ax.set_xlabel('修复效果预期 (%)')
        ax.set_title('修复策略优先级')
        
        plt.tight_layout()
        plt.savefig('kg_diagnosis_report.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✅ 诊断报告已保存: kg_diagnosis_report.png")
    
    def run_full_diagnosis(self):
        """运行完整诊断"""
        print("🔍 开始知识图谱问题诊断...")
        print("=" * 60)
        
        # 1. 加载数据
        if not self.load_data():
            return False
        
        # 2. 诊断建筑功能问题
        function_analysis = self.diagnose_building_functions()
        
        # 3. 诊断形态分类问题  
        height_analysis = self.diagnose_morphology_classification()
        
        # 4. 测试功能映射（如果有合适的字段）
        if function_analysis:
            best_field = max(function_analysis.keys(), 
                           key=lambda x: function_analysis[x]['non_null_rate'])
            print(f"\n🎯 选择完整性最高的字段进行测试: {best_field}")
            self.test_function_mapping(best_field)
        
        # 5. 生成修复参数
        fixed_params = self.generate_fixed_parameters()
        
        # 6. 创建诊断可视化
        try:
            self.create_diagnostic_visualization(height_analysis, function_analysis)
        except Exception as e:
            print(f"⚠️ 可视化生成失败: {e}")
        
        # 7. 输出修复建议
        print("\n" + "=" * 60)
        print("🎯 问题诊断总结与修复建议:")
        
        print("\n❌ 发现的问题:")
        print("   1. 建筑功能分类失效 - 所有建筑都被归为'其他'类型")
        print("   2. 建筑形态分类不完整 - 缺少中高层建筑类型") 
        print("   3. 分类阈值设置不当 - 与实际数据分布不匹配")
        
        print("\n💡 修复建议:")
        print("   1. 重新检查建筑功能字段，选择完整性最高的字段")
        print("   2. 降低楼层分类阈值: [2,4,8] → [1.5,3,6]")
        print("   3. 扩展功能映射规则，增加模糊匹配和多语言支持")
        print("   4. 优化Spacematrix指标计算逻辑")
        print("   5. 增加数据质量检查和清洗步骤")
        
        print(f"\n🔧 建议的新参数:")
        for key, value in fixed_params.items():
            if key in ['fsi_thresholds', 'gsi_thresholds', 'l_thresholds']:
                print(f"   {key}: {value}")
        
        return True


# 使用示例
def main():
    # 配置文件路径
    building_path = r"C:\Users\<USER>\Desktop\22\9-建筑物数据\沈阳建筑物三环.shp"
    l5_path = r"C:\Users\<USER>\Desktop\22\8-街区数据\沈河L5.shp"
    
    # 创建诊断工具
    diagnosis = KGDiagnosisAndFix(building_path, l5_path)
    
    # 运行诊断
    diagnosis.run_full_diagnosis()


if __name__ == "__main__":
    main()