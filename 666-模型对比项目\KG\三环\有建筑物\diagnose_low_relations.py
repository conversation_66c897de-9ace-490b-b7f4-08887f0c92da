"""
诊断脚本：分析关系数量少的原因
直接读取数据集进行深度分析
"""

import pandas as pd
import geopandas as gpd
import numpy as np
from shapely.geometry import Point
import os
from collections import defaultdict

# 数据路径配置
DATA_PATHS = {
    "l4_shp_path": r"C:\Users\<USER>\Desktop\22\8-街区数据\沈河L4.shp",
    "l5_shp_path": r"C:\Users\<USER>\Desktop\22\8-街区数据\沈河L5.shp",
    "poi_path": r"C:\Users\<USER>\Desktop\22\6-POI数据\merged_poi.shp",
    "bc_path": r"C:\Users\<USER>\Desktop\22\15-商圈数据\沈阳商圈.shp",
    "building_path": r"C:\Users\<USER>\Desktop\22\9-建筑物数据\沈阳建筑物三环.shp",
    "land_use_path": r"C:\Users\<USER>\Desktop\22\14-地块数据\沈阳市.shp",
}

def load_and_analyze_data():
    """加载并分析所有数据"""
    print("🔍 加载数据集进行深度分析...")
    print("="*80)
    
    # 加载数据
    datasets = {}
    for name, path in DATA_PATHS.items():
        try:
            if os.path.exists(path):
                if path.endswith('.shp'):
                    data = gpd.read_file(path)
                else:
                    data = pd.read_csv(path)
                datasets[name] = data
                print(f"✅ {name}: {len(data):,} 条记录")
                print(f"   字段: {list(data.columns)}")
                if hasattr(data, 'crs') and data.crs:
                    print(f"   坐标系: {data.crs}")
                print()
            else:
                print(f"❌ {name}: 文件不存在 - {path}")
                datasets[name] = None
        except Exception as e:
            print(f"❌ {name}: 加载失败 - {e}")
            datasets[name] = None
    
    return datasets

def analyze_density_influences(l4_gdf):
    """分析densityInfluences关系生成条件"""
    print("🔍 分析 densityInfluences 关系...")
    print("-"*60)
    
    if l4_gdf is None or l4_gdf.empty:
        print("❌ L4数据不可用")
        return
    
    print(f"L4区域总数: {len(l4_gdf)}")
    
    # 检查是否有POI密度相关字段
    density_fields = [col for col in l4_gdf.columns if 'density' in col.lower() or 'poi' in col.lower()]
    print(f"密度相关字段: {density_fields}")
    
    # 计算理论上的区域对数量
    total_pairs = len(l4_gdf) * (len(l4_gdf) - 1) // 2
    print(f"理论区域对数量: {total_pairs}")
    
    # 分析区域间距离分布
    if len(l4_gdf) > 1:
        print("分析区域间距离分布...")
        distances = []
        centroids = l4_gdf.geometry.centroid
        
        for i in range(len(centroids)):
            for j in range(i+1, len(centroids)):
                dist = centroids.iloc[i].distance(centroids.iloc[j])
                distances.append(dist)
        
        distances = np.array(distances)
        print(f"  距离统计:")
        print(f"    最小距离: {distances.min():.1f}m")
        print(f"    最大距离: {distances.max():.1f}m")
        print(f"    平均距离: {distances.mean():.1f}m")
        print(f"    中位数距离: {np.median(distances):.1f}m")
        
        # 分析不同距离阈值下的关系数量
        thresholds = [500, 1000, 1500, 2000, 3000]
        for threshold in thresholds:
            count = np.sum(distances <= threshold)
            print(f"    距离≤{threshold}m的区域对: {count} ({count/len(distances)*100:.1f}%)")

def analyze_provide_service(bc_gdf, l4_gdf):
    """分析provideService关系生成条件"""
    print("\n🔍 分析 provideService 关系...")
    print("-"*60)
    
    if bc_gdf is None or bc_gdf.empty:
        print("❌ 商圈数据不可用")
        return
    
    if l4_gdf is None or l4_gdf.empty:
        print("❌ L4数据不可用")
        return
    
    print(f"商圈总数: {len(bc_gdf)}")
    print(f"L4区域总数: {len(l4_gdf)}")
    print(f"理论最大关系数: {len(bc_gdf) * len(l4_gdf)}")
    
    # 检查商圈和区域的空间分布
    print("\n检查空间分布...")
    
    # 转换到相同坐标系
    if bc_gdf.crs != l4_gdf.crs:
        print("  转换坐标系...")
        if bc_gdf.crs and l4_gdf.crs:
            bc_gdf = bc_gdf.to_crs(l4_gdf.crs)
    
    # 分析空间重叠
    overlap_count = 0
    intersect_count = 0
    contain_count = 0
    
    for _, bc in bc_gdf.iterrows():
        for _, region in l4_gdf.iterrows():
            if bc.geometry.intersects(region.geometry):
                intersect_count += 1
                if bc.geometry.contains(region.geometry.centroid):
                    contain_count += 1
                if bc.geometry.overlaps(region.geometry):
                    overlap_count += 1
    
    print(f"  空间相交关系: {intersect_count}")
    print(f"  商圈包含区域中心点: {contain_count}")
    print(f"  几何重叠关系: {overlap_count}")
    
    # 分析商圈大小
    bc_areas = bc_gdf.geometry.area
    print(f"\n商圈面积统计:")
    print(f"  最小面积: {bc_areas.min():.1f}m²")
    print(f"  最大面积: {bc_areas.max():.1f}m²")
    print(f"  平均面积: {bc_areas.mean():.1f}m²")

def analyze_border_by(l4_gdf):
    """分析borderBy关系生成条件"""
    print("\n🔍 分析 borderBy 关系...")
    print("-"*60)
    
    if l4_gdf is None or l4_gdf.empty:
        print("❌ L4数据不可用")
        return
    
    print(f"L4区域总数: {len(l4_gdf)}")
    
    # 分析边界相接关系
    border_count = 0
    touch_count = 0
    
    for i in range(len(l4_gdf)):
        for j in range(i+1, len(l4_gdf)):
            geom1 = l4_gdf.iloc[i].geometry
            geom2 = l4_gdf.iloc[j].geometry
            
            if geom1.touches(geom2):
                touch_count += 1
                # 检查是否是真正的边界相接（不是点接触）
                intersection = geom1.intersection(geom2)
                if hasattr(intersection, 'length') and intersection.length > 0:
                    border_count += 1
    
    print(f"几何相接(touches): {touch_count}")
    print(f"边界相接(有长度): {border_count}")
    
    # 分析区域几何特征
    print(f"\n区域几何特征:")
    areas = l4_gdf.geometry.area
    print(f"  面积范围: {areas.min():.1f} - {areas.max():.1f}m²")
    print(f"  平均面积: {areas.mean():.1f}m²")
    
    # 检查几何有效性
    valid_geoms = l4_gdf.geometry.is_valid.sum()
    print(f"  有效几何: {valid_geoms}/{len(l4_gdf)}")

def analyze_high_convenience(poi_gdf, l4_gdf):
    """分析highConvenience关系生成条件"""
    print("\n🔍 分析 highConvenience 关系...")
    print("-"*60)
    
    if poi_gdf is None or poi_gdf.empty:
        print("❌ POI数据不可用")
        return
    
    if l4_gdf is None or l4_gdf.empty:
        print("❌ L4数据不可用")
        return
    
    print(f"POI总数: {len(poi_gdf)}")
    print(f"L4区域总数: {len(l4_gdf)}")
    
    # 检查POI类别分布
    if 'main_cat' in poi_gdf.columns:
        print(f"\nPOI类别分布:")
        category_counts = poi_gdf['main_cat'].value_counts()
        for cat, count in category_counts.head(10).items():
            print(f"  {cat}: {count}")
        
        # 分析高吸引力POI类型
        high_attraction_categories = [
            "购物服务", "餐饮服务", "体育休闲服务", 
            "旅游景点", "教育文化服务", "交通设施服务",
            "医疗保健服务", "商务住宅"
        ]
        
        high_attraction_pois = poi_gdf[poi_gdf['main_cat'].isin(high_attraction_categories)]
        print(f"\n高吸引力POI数量: {len(high_attraction_pois)}/{len(poi_gdf)} ({len(high_attraction_pois)/len(poi_gdf)*100:.1f}%)")
        
        # 分析每个区域的POI密度
        print(f"\n分析区域POI密度...")
        
        # 转换坐标系
        if poi_gdf.crs != l4_gdf.crs:
            if poi_gdf.crs and l4_gdf.crs:
                poi_gdf = poi_gdf.to_crs(l4_gdf.crs)
        
        region_poi_counts = []
        for _, region in l4_gdf.iterrows():
            # 计算区域内POI数量
            pois_in_region = poi_gdf[poi_gdf.geometry.within(region.geometry)]
            poi_count = len(pois_in_region)
            poi_density = poi_count / (region.geometry.area / 1000000)  # 每平方公里POI数
            region_poi_counts.append((poi_count, poi_density))
        
        poi_counts = [x[0] for x in region_poi_counts]
        poi_densities = [x[1] for x in region_poi_counts]
        
        print(f"  区域POI数量统计:")
        print(f"    最小: {min(poi_counts)}")
        print(f"    最大: {max(poi_counts)}")
        print(f"    平均: {np.mean(poi_counts):.1f}")
        print(f"    中位数: {np.median(poi_counts):.1f}")
        
        print(f"  区域POI密度统计(个/km²):")
        print(f"    最小: {min(poi_densities):.1f}")
        print(f"    最大: {max(poi_densities):.1f}")
        print(f"    平均: {np.mean(poi_densities):.1f}")
        
        # 分析便利性阈值
        convenience_threshold = np.percentile(poi_densities, 75)  # 前25%
        convenient_regions = sum(1 for d in poi_densities if d >= convenience_threshold)
        print(f"  便利性阈值(75分位数): {convenience_threshold:.1f}")
        print(f"  高便利性区域数量: {convenient_regions}")

def analyze_near_by(l4_gdf):
    """分析nearBy关系生成条件"""
    print("\n🔍 分析 nearBy 关系...")
    print("-"*60)
    
    if l4_gdf is None or l4_gdf.empty:
        print("❌ L4数据不可用")
        return
    
    print(f"L4区域总数: {len(l4_gdf)}")
    
    # 分析近距离关系的阈值设置
    near_threshold = 1000  # 假设阈值为1000米
    print(f"近距离阈值: {near_threshold}m")
    
    # 计算区域中心点间距离
    centroids = l4_gdf.geometry.centroid
    near_count = 0
    total_pairs = 0
    
    distances = []
    for i in range(len(centroids)):
        for j in range(i+1, len(centroids)):
            distance = centroids.iloc[i].distance(centroids.iloc[j])
            distances.append(distance)
            total_pairs += 1
            
            if distance <= near_threshold:
                near_count += 1
    
    print(f"总区域对数: {total_pairs}")
    print(f"近距离区域对数: {near_count}")
    print(f"近距离比例: {near_count/total_pairs*100:.1f}%")
    
    # 分析距离分布
    distances = np.array(distances)
    print(f"\n距离分布统计:")
    print(f"  最小距离: {distances.min():.1f}m")
    print(f"  最大距离: {distances.max():.1f}m")
    print(f"  平均距离: {distances.mean():.1f}m")
    print(f"  标准差: {distances.std():.1f}m")
    
    # 不同阈值下的关系数量
    thresholds = [500, 800, 1000, 1200, 1500, 2000]
    print(f"\n不同阈值下的近距离关系数量:")
    for threshold in thresholds:
        count = np.sum(distances <= threshold)
        percentage = count / len(distances) * 100
        print(f"  ≤{threshold}m: {count} ({percentage:.1f}%)")

def analyze_data_quality_issues(datasets):
    """分析数据质量问题"""
    print("\n🔍 数据质量问题分析...")
    print("="*80)
    
    # 检查坐标系一致性
    print("1. 坐标系一致性检查:")
    crs_info = {}
    for name, data in datasets.items():
        if data is not None and hasattr(data, 'crs'):
            crs_info[name] = data.crs
            print(f"  {name}: {data.crs}")
    
    # 检查空间数据有效性
    print("\n2. 空间数据有效性检查:")
    for name, data in datasets.items():
        if data is not None and hasattr(data, 'geometry'):
            valid_count = data.geometry.is_valid.sum()
            total_count = len(data)
            print(f"  {name}: {valid_count}/{total_count} 有效几何 ({valid_count/total_count*100:.1f}%)")
            
            # 检查空几何
            empty_count = data.geometry.is_empty.sum()
            if empty_count > 0:
                print(f"    ⚠️ {empty_count} 个空几何")
    
    # 检查数据范围
    print("\n3. 数据空间范围检查:")
    for name, data in datasets.items():
        if data is not None and hasattr(data, 'geometry') and not data.empty:
            bounds = data.total_bounds
            print(f"  {name}: [{bounds[0]:.1f}, {bounds[1]:.1f}, {bounds[2]:.1f}, {bounds[3]:.1f}]")

def main():
    """主函数"""
    print("🚀 开始分析关系数量少的原因")
    print("="*80)
    
    # 1. 加载数据
    datasets = load_and_analyze_data()
    
    # 2. 分析各种关系的生成条件
    analyze_density_influences(datasets.get('l4_shp_path'))
    analyze_provide_service(datasets.get('bc_path'), datasets.get('l4_shp_path'))
    analyze_border_by(datasets.get('l4_shp_path'))
    analyze_high_convenience(datasets.get('poi_path'), datasets.get('l4_shp_path'))
    analyze_near_by(datasets.get('l4_shp_path'))
    
    # 3. 数据质量问题分析
    analyze_data_quality_issues(datasets)
    
    print("\n" + "="*80)
    print("🎯 分析完成")
    print("请根据上述分析结果确定关系数量少的具体原因")

if __name__ == "__main__":
    main()
