{"POI": {"count": 21727, "entities": ["POI_104492", "POI_104493", "POI_104494", "POI_104886", "POI_104895", "POI_104897", "POI_104908", "POI_104911", "POI_104912", "POI_104917", "POI_104918", "POI_104919", "POI_104932", "POI_104936", "POI_104947", "POI_104950", "POI_104951", "POI_104952", "POI_104953", "POI_104955", "POI_104958", "POI_104960", "POI_104963", "POI_104967", "POI_104970", "POI_104971", "POI_104978", "POI_104981", "POI_104982", "POI_104986", "POI_104988", "POI_104990", "POI_104992", "POI_105231", "POI_105256", "POI_105257", "POI_105258", "POI_105259", "POI_105260", "POI_105262", "POI_105263", "POI_105264", "POI_105266", "POI_105267", "POI_105268", "POI_105270", "POI_105272", "POI_105274", "POI_105275", "POI_105276", "POI_106124", "POI_106149", "POI_106150", "POI_106153", "POI_106154", "POI_106160", "POI_106166", "POI_106179", "POI_106180", "POI_106183", "POI_106505", "POI_106511", "POI_106512", "POI_106513", "POI_106514", "POI_106515", "POI_106516", "POI_106517", "POI_106518", "POI_106519", "POI_106520", "POI_106521", "POI_106522", "POI_106523", "POI_106524", "POI_106525", "POI_106526", "POI_106527", "POI_106528", "POI_106529", "POI_106530", "POI_106531", "POI_106532", "POI_106533", "POI_106534", "POI_106535", "POI_106536", "POI_106537", "POI_106538", "POI_106539", "POI_106540", "POI_106541", "POI_106542", "POI_106543", "POI_106544", "POI_106545", "POI_106546", "POI_106547", "POI_106548", "POI_106549"]}, "Region": {"count": 122, "entities": ["Region_150", "Region_152", "Region_153", "Region_154", "Region_156", "Region_157", "Region_158", "Region_159", "Region_160", "Region_161", "Region_162", "Region_163", "Region_164", "Region_165", "Region_166", "Region_187", "Region_188", "Region_189", "Region_190", "Region_191", "Region_192", "Region_193", "Region_194", "Region_195", "Region_196", "Region_197", "Region_198", "Region_199", "Region_200", "Region_201", "Region_202", "Region_203", "Region_204", "Region_205", "Region_206", "Region_207", "Region_208", "Region_209", "Region_210", "Region_211", "Region_212", "Region_213", "Region_214", "Region_219", "Region_220", "Region_221", "Region_222", "Region_223", "Region_224", "Region_225", "Region_227", "Region_228", "Region_229", "Region_230", "Region_518", "Region_519", "Region_520", "Region_521", "Region_523", "Region_524", "Region_525", "Region_590", "Region_591", "Region_595", "Region_596", "Region_597", "Region_598", "Region_599", "Region_600", "Region_601", "Region_602", "Region_603", "Region_604", "Region_605", "Region_606", "Region_607", "Region_608", "Region_609", "Region_623", "Region_624", "Region_630", "Region_631", "Region_633", "Region_634", "Region_635", "Region_636", "Region_637", "Region_638", "Region_640", "Region_643", "Region_644", "Region_645", "Region_646", "Region_647", "Region_648", "Region_653", "Region_655", "Region_656", "Region_660", "Region_661"]}, "Category": {"count": 14, "entities": ["Cate_0", "Cate_1", "Cate_10", "Cate_12", "Cate_14", "Cate_15", "Cate_2", "Cate_3", "Cate_4", "Cate_5", "Cate_6", "Cate_7", "Cate_8", "Cate_9"]}, "BusinessCircle": {"count": 5, "entities": ["BC_173", "BC_175", "BC_178", "BC_179", "BC_180"]}}