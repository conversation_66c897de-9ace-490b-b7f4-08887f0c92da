================================================================================
知识图谱诊断报告 (增强版)
================================================================================
生成时间: 2025-06-02 22:49:39
诊断开始时间: 2025-06-02 22:47:28
诊断耗时: 131.0 秒

1. 数据概览
----------------------------------------
建筑物数据: 87,163 条记录
L5地块数据: 412 条记录
土地利用数据: 3,446 条记录
POI数据: 182,854 条记录
知识图谱三元组: 113,016 条记录

2. 诊断结果详情
----------------------------------------

LAND_USE_ANALYSIS:
{'recommended_field': 'Level2_cn', 'field_analysis': {'Level1': {'total_count': 3446, 'non_null_count': 3446, 'non_null_rate': 100.0, 'unique_count': 5, 'unique_values': [5, 1, 4, 3, 2], 'value_distribution': {5: 1247, 1: 1106, 3: 661, 2: 339, 4: 93}, 'top_10_values': {5: 1247, 1: 1106, 3: 661, 2: 339, 4: 93}}, 'Level2': {'total_count': 3446, 'non_null_count': 3446, 'non_null_rate': 100.0, 'unique_count': 11, 'unique_values': [505, 101, 502, 403, 301, 503, 501, 202, 504, 201, 402], 'value_distribution': {101: 1106, 505: 743, 301: 661, 202: 241, 502: 214, 501: 125, 503: 101, 201: 98, 403: 72, 504: 64, 402: 21}, 'top_10_values': {101: 1106, 505: 743, 301: 661, 202: 241, 502: 214, 501: 125, 503: 101, 201: 98, 403: 72, 504: 64}}, 'Level1_cn': {'total_count': 3446, 'non_null_count': 3446, 'non_null_rate': 100.0, 'unique_count': 5, 'unique_values': ['公共管理和服务用地', '居住用地', '交通用地', '工业用地', '商业用地'], 'value_distribution': {'公共管理和服务用地': 1247, '居住用地': 1106, '工业用地': 661, '商业用地': 339, '交通用地': 93}, 'top_10_values': {'公共管理和服务用地': 1247, '居住用地': 1106, '工业用地': 661, '商业用地': 339, '交通用地': 93}}, 'Level2_cn': {'total_count': 3446, 'non_null_count': 3446, 'non_null_rate': 100.0, 'unique_count': 11, 'unique_values': ['公园与绿地用地', '居住用地', '教育科研用地', '机场设施用地', '工业用地', '医疗卫生用地', '行政办公用地', '商业服务用地', '体育与文化用地', '商务办公用地', '交通场站用地'], 'value_distribution': {'居住用地': 1106, '公园与绿地用地': 743, '工业用地': 661, '商业服务用地': 241, '教育科研用地': 214, '行政办公用地': 125, '医疗卫生用地': 101, '商务办公用地': 98, '机场设施用地': 72, '体育与文化用地': 64, '交通场站用地': 21}, 'top_10_values': {'居住用地': 1106, '公园与绿地用地': 743, '工业用地': 661, '商业服务用地': 241, '教育科研用地': 214, '行政办公用地': 125, '医疗卫生用地': 101, '商务办公用地': 98, '机场设施用地': 72, '体育与文化用地': 64}}}}

DENSITY_ANALYSIS:
{'land_building_density': {'min_density': 0.0, 'max_density': 0.0, 'mean_density': 0.0, 'median_density': 0.0, 'std_density': 0.0, 'density_distribution': {'low_density (0-5)': 412, 'medium_density (5-20)': 0, 'high_density (20+)': 0}}, 'height_distribution': {'field_name': 'Height', 'total_buildings': 87163, 'buildings_with_height': 87163, 'height_stats': {'min': 0.0, 'max': 103.01041506419357, 'mean': 13.018662809187404, 'median': 11.982007114537971, 'std': 6.549128834207453}, 'height_categories': {'low_rise (≤18m)': 71712, 'mid_rise (18-50m)': 15342, 'high_rise (50-100m)': 107, 'super_high_rise (>100m)': 2}}}

DATA_QUALITY:
{'building_quality': {'total_records': 87163, 'geometry_validity': 87163, 'null_geometries': 0, 'field_completeness': {'merged_id': {'non_null_count': 87163, 'completeness_rate': 100.0}, 'Height': {'non_null_count': 87163, 'completeness_rate': 100.0}, 'Function': {'non_null_count': 87163, 'completeness_rate': 100.0}, 'Age': {'non_null_count': 87163, 'completeness_rate': 100.0}, 'Quality': {'non_null_count': 87163, 'completeness_rate': 100.0}, '建筑ID': {'non_null_count': 87163, 'completeness_rate': 100.0}}, 'coordinate_range': {'min_x': 13721916.719866501, 'max_x': 13756046.390761284, 'min_y': 5115600.188835461, 'max_y': 5147550.939846336}}, 'landuse_quality': {'total_records': 3446, 'geometry_validity': 3446, 'null_geometries': 0, 'field_completeness': {'Lon': {'non_null_count': 3446, 'completeness_rate': 100.0}, 'Lat': {'non_null_count': 3446, 'completeness_rate': 100.0}, 'F_AREA': {'non_null_count': 3446, 'completeness_rate': 100.0}, 'City_CODE': {'non_null_count': 3446, 'completeness_rate': 100.0}, 'UUID': {'non_null_count': 3446, 'completeness_rate': 100.0}, 'Level1': {'non_null_count': 3446, 'completeness_rate': 100.0}, 'Level2': {'non_null_count': 3446, 'completeness_rate': 100.0}, 'Level1_cn': {'non_null_count': 3446, 'completeness_rate': 100.0}, 'Level2_cn': {'non_null_count': 3446, 'completeness_rate': 100.0}}}}

KG_STATISTICS:
{'total_triples': 113016, 'unique_relations': 18, 'unique_entities': 36045, 'relation_distribution': {'locateAt': 21727, 'cateOf': 21727, 'hasFunctionBuilding': 13746, 'withinRegion': 12824, 'belongsToBuilding': 11985, 'connectedTo': 9132, 'similarFunction': 6774, 'belongTo': 6539, 'flowTransition': 4928, 'nearBy': 1383, 'highConvenience': 636, 'borderBy': 580, 'hasMorphology': 412, 'morphologySimilar': 331, 'belongsToLand': 99, 'hasLandUse': 83, 'provideService': 55, 'densityInfluences': 55}, 'entity_type_distribution': {'Building': 13746, 'POI': 21727, 'Land': 412, 'Region': 122, 'Function': 6, 'Other': 19, 'Morphology': 11, 'LandUse': 2}}

3. 修复建议
----------------------------------------
1. 重新检查建筑功能字段，选择完整性最高的字段
2. 降低楼层分类阈值: [2,4,8] → [1.5,3,6]
3. 扩展功能映射规则，增加模糊匹配和多语言支持
4. 优化Spacematrix指标计算逻辑
5. 增加数据质量检查和清洗步骤
6. 使用Level2_cn字段进行土地利用分类
7. 基于密度分布调整密度影响关系阈值
