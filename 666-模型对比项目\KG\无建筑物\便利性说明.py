"""
街区功能便利性(highConvenience)判断逻辑详细解释
"""

def check_convenience(region_pois):
    """
    街区功能便利性判断的核心逻辑
    
    ==================== 便利性判断原理 ====================
    
    便利性的核心理念：一个街区是否能为居民提供完整、便捷的生活服务
    
    判断维度：
    1. 服务种类多样性（类别丰富度）
    2. 服务规模充足性（POI数量）  
    3. 基本服务完整性（必需服务全覆盖）
    4. 综合便利性得分（量化评估）
    
    ==================== 具体判断标准 ====================
    
    【硬性条件】- 必须全部满足，否则直接判定为不便利
    1. POI类别数量 ≥ 7种        （服务种类够丰富）
    2. POI总数 ≥ 20个           （服务规模够充足）
    3. 基本服务类别必须全覆盖    （生活必需服务完整）
       - 餐饮服务：解决吃饭问题
       - 购物服务：解决购物需求  
       - 生活服务：解决日常生活服务（理发、维修、洗衣等）
       - 交通设施服务：解决出行需求
    4. 综合便利性得分 ≥ 12分     （量化评估达标）
    
    ==================== 便利性得分计算逻辑 ====================
    
    得分构成（累加制）：
    基础分：
    - 每种POI类别 +1分           （类别多样性奖励）
    - 基本服务类别每种 +2分       （必需服务重点加分）
    
    奖励分：
    - POI总数超过30个 +2分        （中等规模奖励）
    - POI总数超过50个 +3分        （大规模奖励，与上面不累加）
    - 覆盖所有基本服务类别 +3分    （完整性奖励）
    
    示例计算：
    假设某街区有：餐饮(5个)、购物(8个)、生活服务(3个)、交通设施(2个)、
                教育文化(4个)、医疗保健(3个)、体育休闲(2个)，共7类，27个POI
    
    得分计算：
    - 7种类别：7分
    - 4种基本服务：4×2=8分  
    - 27个POI(未超30): 0分
    - 全覆盖基本服务：3分
    总分：7+8+0+3=18分 ≥ 12分 → 便利性街区
    
    ==================== 关系建立逻辑 ====================
    
    便利性关系不是街区指向抽象概念，而是便利性街区之间的相互关系：
    
    1. 识别阶段：找出所有满足便利性标准的街区
    2. 距离筛选：计算便利性街区两两之间的距离
    3. 关系建立：距离 < 1000米的便利性街区间建立 highConvenience 关系
    
    关系语义：
    Region_A highConvenience Region_B 
    表示：A和B都是功能便利的街区，且空间距离适中，形成便利性服务网络
    
    作用：
    - 识别城市功能中心区域
    - 分析便利性服务的空间聚集
    - 为城市规划提供便利性网络参考
    """
    
    if region_pois.empty:
        return False, 0
    
    # ==================== 1. 基础统计 ====================
    total_pois = len(region_pois)                          # POI总数
    categories = set(region_pois['main_cat'].dropna())     # 所有POI类别集合
    category_count = len(categories)                       # POI类别数量
    
    print(f"街区POI统计：总数={total_pois}, 类别数={category_count}")
    print(f"包含类别：{list(categories)}")
    
    # ==================== 2. 硬性条件检查 ====================
    
    # 条件1：类别数量检查
    if category_count < PARAMS["convenience_min_categories"]:
        print(f"❌ 类别数量不足：{category_count} < {PARAMS['convenience_min_categories']}")
        return False, 0
    
    # 条件2：POI总数检查  
    if total_pois < PARAMS["convenience_min_pois"]:
        print(f"❌ POI总数不足：{total_pois} < {PARAMS['convenience_min_pois']}")
        return False, 0
    
    # 条件3：基本服务类别全覆盖检查
    essential_cats = set(PARAMS["convenience_essential_categories"])  # 必需的4种基本服务
    covered_essential = essential_cats.intersection(categories)       # 实际覆盖的基本服务
    missing_essential = essential_cats - covered_essential            # 缺失的基本服务
    
    print(f"基本服务覆盖情况：")
    print(f"  必需服务：{list(essential_cats)}")
    print(f"  已覆盖：{list(covered_essential)}")
    print(f"  缺失：{list(missing_essential)}")
    
    if len(covered_essential) < 4:  # 必须覆盖所有4种基本服务
        print(f"❌ 基本服务覆盖不全：{len(covered_essential)}/4")
        return False, 0
    
    # ==================== 3. 便利性得分计算 ====================
    convenience_score = 0
    score_details = []
    
    # 基础分1：每种类别+1分
    category_score = category_count
    convenience_score += category_score
    score_details.append(f"类别多样性：{category_count}种 × 1分 = {category_score}分")
    
    # 基础分2：基本服务类别每种+2分
    essential_score = len(covered_essential) * 2
    convenience_score += essential_score
    score_details.append(f"基本服务：{len(covered_essential)}种 × 2分 = {essential_score}分")
    
    # 奖励分1：POI数量奖励（阶梯制，不累加）
    if total_pois > 50:
        poi_bonus = 3
        convenience_score += poi_bonus
        score_details.append(f"POI规模奖励：{total_pois}个 > 50个，奖励{poi_bonus}分")
    elif total_pois > 30:
        poi_bonus = 2
        convenience_score += poi_bonus
        score_details.append(f"POI规模奖励：{total_pois}个 > 30个，奖励{poi_bonus}分")
    else:
        score_details.append(f"POI规模奖励：{total_pois}个 ≤ 30个，无奖励")
    
    # 奖励分2：全覆盖基本服务奖励
    if len(covered_essential) == 4:
        complete_bonus = 3
        convenience_score += complete_bonus
        score_details.append(f"完整性奖励：全覆盖4种基本服务，奖励{complete_bonus}分")
    
    # ==================== 4. 最终判断 ====================
    print("\n得分详情：")
    for detail in score_details:
        print(f"  {detail}")
    print(f"总得分：{convenience_score}分")
    print(f"门槛：{PARAMS['convenience_score_threshold']}分")
    
    is_convenient = convenience_score >= PARAMS["convenience_score_threshold"]
    
    if is_convenient:
        print(f"✅ 便利性街区认定成功！")
    else:
        print(f"❌ 便利性得分不足：{convenience_score} < {PARAMS['convenience_score_threshold']}")
    
    return is_convenient, convenience_score


# ==================== POI类别说明 ====================
"""
POI类别体系（基于高德地图POI分类标准）：

【必需的基本服务类别】（4种）：
1. 餐饮服务
   - 包含：中餐厅、西餐厅、快餐店、小吃店、咖啡厅、茶餐厅等
   - 作用：解决日常饮食需求
   - 重要性：生活必需，使用频率最高

2. 购物服务  
   - 包含：超市、便利店、商场、专卖店、市场等
   - 作用：解决日常购物需求（食品、日用品、服装等）
   - 重要性：生活必需，覆盖各类商品需求

3. 生活服务
   - 包含：理发店、洗衣店、维修店、家政服务、快递点等
   - 作用：解决日常生活服务需求
   - 重要性：提高生活品质，必不可少

4. 交通设施服务
   - 包含：公交站、地铁站、停车场、加油站等  
   - 作用：解决出行和交通需求
   - 重要性：连接外界，流动性保障

【其他重要类别】（用于丰富性评估）：
5. 医疗保健服务
   - 包含：医院、诊所、药店、体检中心等
   - 作用：健康保障

6. 教育文化服务
   - 包含：学校、图书馆、培训机构、文化场馆等
   - 作用：教育和文化需求

7. 体育休闲服务
   - 包含：健身房、体育场馆、公园、娱乐场所等
   - 作用：休闲娱乐和健身

8. 商务住宅
   - 包含：办公楼、住宅小区、酒店等
   - 作用：工作和居住

9. 政府机构及社会团体
   - 包含：政府部门、社区服务中心、银行等
   - 作用：公共服务

10. 旅游景点
    - 包含：景区、名胜古迹、主题公园等
    - 作用：旅游观光

选择这4种作为基本服务的原因：
1. 使用频率高：居民日常生活中最常用到
2. 不可替代性：缺少任何一种都会显著影响生活便利性
3. 空间依赖性：需要就近获取服务，不适合远距离获取
4. 普适性：适用于所有人群和生活方式
"""