{"POI": {"count": 21727, "entities": ["POI_104492", "POI_104493", "POI_104494", "POI_104886", "POI_104895", "POI_104897", "POI_104908", "POI_104911", "POI_104912", "POI_104917", "POI_104918", "POI_104919", "POI_104932", "POI_104936", "POI_104947", "POI_104950", "POI_104951", "POI_104952", "POI_104953", "POI_104955", "POI_104958", "POI_104960", "POI_104963", "POI_104967", "POI_104970", "POI_104971", "POI_104978", "POI_104981", "POI_104982", "POI_104986", "POI_104988", "POI_104990", "POI_104992", "POI_105231", "POI_105256", "POI_105257", "POI_105258", "POI_105259", "POI_105260", "POI_105262", "POI_105263", "POI_105264", "POI_105266", "POI_105267", "POI_105268", "POI_105270", "POI_105272", "POI_105274", "POI_105275", "POI_105276", "POI_106124", "POI_106149", "POI_106150", "POI_106153", "POI_106154", "POI_106160", "POI_106166", "POI_106179", "POI_106180", "POI_106183", "POI_106505", "POI_106511", "POI_106512", "POI_106513", "POI_106514", "POI_106515", "POI_106516", "POI_106517", "POI_106518", "POI_106519", "POI_106520", "POI_106521", "POI_106522", "POI_106523", "POI_106524", "POI_106525", "POI_106526", "POI_106527", "POI_106528", "POI_106529", "POI_106530", "POI_106531", "POI_106532", "POI_106533", "POI_106534", "POI_106535", "POI_106536", "POI_106537", "POI_106538", "POI_106539", "POI_106540", "POI_106541", "POI_106542", "POI_106543", "POI_106544", "POI_106545", "POI_106546", "POI_106547", "POI_106548", "POI_106549"]}, "Building": {"count": 13746, "entities": ["Building_44486", "Building_44487", "Building_44488", "Building_44490", "Building_44493", "Building_44497", "Building_45347", "Building_45349", "Building_45350", "Building_45352", "Building_45353", "Building_45355", "Building_45356", "Building_45357", "Building_45358", "Building_45359", "Building_45360", "Building_45361", "Building_45362", "Building_45363", "Building_45364", "Building_45365", "Building_45366", "Building_45367", "Building_45368", "Building_45369", "Building_45370", "Building_45371", "Building_45372", "Building_45373", "Building_45374", "Building_45375", "Building_45376", "Building_45377", "Building_45378", "Building_45379", "Building_45380", "Building_45381", "Building_45382", "Building_45383", "Building_45384", "Building_45385", "Building_45386", "Building_45387", "Building_45388", "Building_45389", "Building_45390", "Building_45391", "Building_45392", "Building_45393", "Building_45394", "Building_45395", "Building_45396", "Building_45397", "Building_45398", "Building_45399", "Building_45400", "Building_45401", "Building_45402", "Building_45403", "Building_45404", "Building_45405", "Building_45406", "Building_45407", "Building_45408", "Building_45409", "Building_45411", "Building_45412", "Building_45413", "Building_45414", "Building_45415", "Building_45420", "Building_45421", "Building_45424", "Building_45426", "Building_45428", "Building_46099", "Building_46101", "Building_46105", "Building_46106", "Building_46107", "Building_46108", "Building_46109", "Building_46110", "Building_46111", "Building_46112", "Building_46113", "Building_46114", "Building_46115", "Building_46116", "Building_46117", "Building_46118", "Building_46119", "Building_46120", "Building_46121", "Building_46122", "Building_46123", "Building_46124", "Building_46125", "Building_46126"]}, "Land": {"count": 412, "entities": ["Land_0", "Land_1", "Land_10", "Land_100", "Land_101", "Land_102", "Land_103", "Land_104", "Land_105", "Land_106", "Land_107", "Land_108", "Land_109", "Land_11", "Land_110", "Land_111", "Land_112", "Land_113", "Land_114", "Land_115", "Land_116", "Land_117", "Land_118", "Land_119", "Land_12", "Land_120", "Land_121", "Land_122", "Land_123", "Land_124", "Land_125", "Land_126", "Land_127", "Land_128", "Land_129", "Land_13", "Land_130", "Land_131", "Land_132", "Land_133", "Land_134", "Land_135", "Land_136", "Land_137", "Land_138", "Land_139", "Land_14", "Land_140", "Land_141", "Land_142", "Land_143", "Land_144", "Land_145", "Land_146", "Land_147", "Land_148", "Land_149", "Land_15", "Land_150", "Land_151", "Land_152", "Land_153", "Land_154", "Land_155", "Land_156", "Land_157", "Land_158", "Land_159", "Land_16", "Land_160", "Land_161", "Land_162", "Land_163", "Land_164", "Land_165", "Land_166", "Land_167", "Land_168", "Land_169", "Land_17", "Land_170", "Land_171", "Land_172", "Land_173", "Land_174", "Land_175", "Land_176", "Land_177", "Land_178", "Land_179", "Land_18", "Land_180", "Land_181", "Land_182", "Land_183", "Land_184", "Land_185", "Land_186", "Land_187", "Land_188"]}, "Region": {"count": 122, "entities": ["Region_150", "Region_152", "Region_153", "Region_154", "Region_156", "Region_157", "Region_158", "Region_159", "Region_160", "Region_161", "Region_162", "Region_163", "Region_164", "Region_165", "Region_166", "Region_187", "Region_188", "Region_189", "Region_190", "Region_191", "Region_192", "Region_193", "Region_194", "Region_195", "Region_196", "Region_197", "Region_198", "Region_199", "Region_200", "Region_201", "Region_202", "Region_203", "Region_204", "Region_205", "Region_206", "Region_207", "Region_208", "Region_209", "Region_210", "Region_211", "Region_212", "Region_213", "Region_214", "Region_219", "Region_220", "Region_221", "Region_222", "Region_223", "Region_224", "Region_225", "Region_227", "Region_228", "Region_229", "Region_230", "Region_518", "Region_519", "Region_520", "Region_521", "Region_523", "Region_524", "Region_525", "Region_590", "Region_591", "Region_595", "Region_596", "Region_597", "Region_598", "Region_599", "Region_600", "Region_601", "Region_602", "Region_603", "Region_604", "Region_605", "Region_606", "Region_607", "Region_608", "Region_609", "Region_623", "Region_624", "Region_630", "Region_631", "Region_633", "Region_634", "Region_635", "Region_636", "Region_637", "Region_638", "Region_640", "Region_643", "Region_644", "Region_645", "Region_646", "Region_647", "Region_648", "Region_653", "Region_655", "Region_656", "Region_660", "Region_661"]}, "Morphology": {"count": 11, "entities": ["Morph_HighRiseHighDensity", "Morph_HighRiseLowDensity", "Morph_HighRiseMidDensity", "Morph_LowRiseHighDensity", "Morph_LowRiseLowDensity", "Morph_LowRiseMidDensity", "Morph_MidRiseHighDensity", "Morph_MidRiseLowDensity", "Morph_MidRiseMidDensity", "Morph_SuperHighRise", "Morph_Vacant"]}, "BusinessCircle": {"count": 5, "entities": ["BC_173", "BC_175", "BC_178", "BC_179", "BC_180"]}, "Category": {"count": 14, "entities": ["Cate_0", "Cate_1", "Cate_10", "Cate_11", "Cate_12", "Cate_13", "Cate_2", "Cate_3", "Cate_4", "Cate_5", "Cate_6", "Cate_7", "Cate_8", "Cate_9"]}, "LandUse": {"count": 2, "entities": ["LandUse_Other", "LandUse_Residential"]}, "Function": {"count": 6, "entities": ["Func_Commercial", "Func_Industrial", "Func_Office", "Func_Other", "Func_Public", "Func_Residential"]}}