"""
测试脚本：演示新增的三元组分析功能
"""

from kg_analyzer import KnowledgeGraphAnalyzer
import os

def test_triple_analysis():
    """测试三元组分析功能"""
    
    # 配置文件路径
    kg_file_path = r"D:\研二\能耗估算\666-模型对比项目\KG\建筑物\kg_with_building_fixed.txt"
    output_dir = r"D:\研二\能耗估算\666-模型对比项目\KG\建筑物\test_triple_analysis_results"
    
    print("🚀 测试新增的三元组分析功能")
    print("="*60)
    
    # 检查文件是否存在
    if not os.path.exists(kg_file_path):
        print(f"❌ 知识图谱文件不存在: {kg_file_path}")
        print("请确保文件路径正确")
        return False
    
    # 创建分析器
    analyzer = KnowledgeGraphAnalyzer(kg_file_path, output_dir)
    
    # 加载数据
    print("1. 加载知识图谱数据...")
    if not analyzer.load_knowledge_graph():
        print("❌ 数据加载失败")
        return False
    
    # 分类实体
    print("\n2. 分类实体...")
    analyzer.classify_entities()
    
    # 分析关系
    print("\n3. 分析关系...")
    analyzer.analyze_relations()
    
    # 测试新功能：三元组模式分析
    print("\n" + "="*60)
    print("🔍 测试新功能：三元组模式分析")
    print("="*60)
    
    try:
        triple_patterns, relation_patterns = analyzer.analyze_triple_patterns()
        print(f"✅ 成功分析了 {len(triple_patterns)} 种三元组模式")
        print(f"✅ 分析了 {len(relation_patterns)} 种关系的实体类型模式")
    except Exception as e:
        print(f"❌ 三元组模式分析失败: {e}")
        return False
    
    # 测试新功能：实体类型交互分析
    print("\n" + "="*60)
    print("🌐 测试新功能：实体类型交互分析")
    print("="*60)
    
    try:
        type_interactions, type_relations = analyzer.analyze_entity_type_interactions()
        total_interactions = sum(sum(targets.values()) for targets in type_interactions.values())
        print(f"✅ 成功分析了 {total_interactions:,} 个实体类型交互")
        print(f"✅ 涉及 {len(type_interactions)} 种源实体类型")
    except Exception as e:
        print(f"❌ 实体类型交互分析失败: {e}")
        return False
    
    # 测试新功能：全面三元组报告生成
    print("\n" + "="*60)
    print("📝 测试新功能：全面三元组报告生成")
    print("="*60)
    
    try:
        report_filename = analyzer.generate_comprehensive_triple_report()
        print(f"✅ 成功生成全面三元组分析报告: {report_filename}")
    except Exception as e:
        print(f"❌ 全面三元组报告生成失败: {e}")
        return False
    
    # 显示分析总结
    print("\n" + "="*60)
    print("📊 分析总结")
    print("="*60)
    
    print(f"三元组总数: {len(analyzer.triples):,}")
    print(f"实体总数: {len(analyzer.entities):,}")
    print(f"关系类型数: {len(analyzer.relations):,}")
    print(f"实体类型数: {len(analyzer.entity_stats):,}")
    
    # 显示前5个最频繁的关系
    print(f"\n前5个最频繁的关系:")
    sorted_relations = sorted(analyzer.relation_stats.items(), key=lambda x: x[1], reverse=True)[:5]
    for i, (relation, count) in enumerate(sorted_relations, 1):
        percentage = count / len(analyzer.triples) * 100
        print(f"  {i}. {relation}: {count:,} ({percentage:.1f}%)")
    
    # 显示前5个最多的实体类型
    print(f"\n前5个最多的实体类型:")
    sorted_entities = sorted(analyzer.entity_stats.items(), key=lambda x: x[1], reverse=True)[:5]
    for i, (entity_type, count) in enumerate(sorted_entities, 1):
        percentage = count / len(analyzer.entities) * 100
        print(f"  {i}. {entity_type}: {count:,} ({percentage:.1f}%)")
    
    print(f"\n🎉 测试完成！结果已保存到: {output_dir}")
    print("\n📄 生成的文件:")
    print("  - comprehensive_triple_analysis_*.txt: 全面三元组分析报告")
    
    return True

def show_new_features():
    """展示新增功能的说明"""
    print("\n🆕 新增功能说明:")
    print("="*60)
    
    print("\n1. 三元组模式分析 (analyze_triple_patterns)")
    print("   - 统计所有三元组的模式 (头实体类型-关系-尾实体类型)")
    print("   - 显示每种模式的数量和百分比")
    print("   - 提供具体的三元组示例")
    print("   - 按关系分析实体类型模式")
    
    print("\n2. 实体类型交互分析 (analyze_entity_type_interactions)")
    print("   - 创建实体类型交互矩阵")
    print("   - 显示类型间的连接数量")
    print("   - 分析最频繁的实体类型交互")
    print("   - 列出每种交互使用的关系类型")
    
    print("\n3. 全面三元组分析报告 (generate_comprehensive_triple_report)")
    print("   - 生成详细的文本报告")
    print("   - 包含所有关系类型列表")
    print("   - 包含所有实体类型列表")
    print("   - 包含三元组模式统计")
    print("   - 包含实体类型交互统计")
    
    print("\n🎯 这些功能的价值:")
    print("   ✅ 全面了解知识图谱的结构")
    print("   ✅ 识别主要的三元组模式")
    print("   ✅ 发现实体类型间的关系")
    print("   ✅ 为图神经网络建模提供洞察")
    print("   ✅ 支持知识图谱质量评估")

if __name__ == "__main__":
    print("🔬 知识图谱三元组分析功能测试")
    print("="*60)
    
    # 显示新功能说明
    show_new_features()
    
    print("\n" + "="*60)
    print("开始测试...")
    
    # 运行测试
    success = test_triple_analysis()
    
    if success:
        print("\n✅ 所有测试通过！新功能工作正常")
    else:
        print("\n❌ 测试失败，请检查错误信息")
    
    print("\n💡 提示：")
    print("   - 如果要运行完整分析，请使用 kg_analyzer.py 的 main() 函数")
    print("   - 新功能已集成到完整分析流程中")
    print("   - 查看生成的报告文件获取详细信息")
