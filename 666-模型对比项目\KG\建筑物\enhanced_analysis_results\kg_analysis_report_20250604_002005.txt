================================================================================
知识图谱分析报告 (增强版)
================================================================================
生成时间: 2025-06-04 00:20:05
分析开始时间: 2025-06-04 00:19:55
分析耗时: 9.6 秒

1. 基本统计信息
----------------------------------------
三元组总数: 107,119
实体总数: 36,046
关系类型数: 17
实体类型数: 9

2. 实体类型分布
----------------------------------------
POI            :   21,727 ( 60.3%)
Building       :   13,746 ( 38.1%)
Land           :      412 (  1.1%)
Region         :      122 (  0.3%)
Category       :       14 (  0.0%)
Morphology     :       11 (  0.0%)
Function       :        6 (  0.0%)
BusinessCircle :        5 (  0.0%)
LandUse        :        3 (  0.0%)

3. 关系类型分布 (前10)
----------------------------------------
cateOf                   :  21,727 ( 20.3%)
locateAt                 :  21,727 ( 20.3%)
hasFunctionBuilding      :  13,746 ( 12.8%)
withinRegion             :  12,824 ( 12.0%)
belongsToBuilding        :  11,985 ( 11.2%)
connectedTo              :   9,132 (  8.5%)
similarFunction          :   6,774 (  6.3%)
flowTransition           :   4,928 (  4.6%)
nearBy                   :   1,383 (  1.3%)
highConvenience          :     636 (  0.6%)

4. 建筑功能类别详情
----------------------------------------
Func_Residential          (住宅      ): 10489 次 ( 76.3%)
Func_Other                (其他      ):   996 次 (  7.2%)
Func_Public               (公共      ):   805 次 (  5.9%)
Func_Office               (办公      ):   769 次 (  5.6%)
Func_Commercial           (商业      ):   686 次 (  5.0%)
Func_Industrial           (工业      ):     1 次 (  0.0%)

5. 土地利用类别详情
----------------------------------------
LandUse_Residential       (居住用地        ):   220 次 ( 53.4%)
LandUse_Other             (其他用地        ):   187 次 ( 45.4%)
LandUse_Industrial        (工业用地        ):     5 次 (  1.2%)

6. 连通性分析
----------------------------------------
总连接数: 107,119
平均连接度: 5.94
图密度: 0.000165

7. 分析总结
----------------------------------------
本次分析涵盖了以下方面：
- 实体类型分布和统计
- 关系类型分析和模式识别
- 建筑功能和形态类别详情
- 土地利用类别分析
- POI类别分布统计
- 建筑物详情分析
- 关系模式和分组分析
- 连通性指标计算
- 可视化图表生成
PS D:\研二\能耗估算\666-模型对比项目\KG> & D:/anaconda3/envs/cs/python.exe d:/研二/能耗估算/666-模型对比项目/KG/建筑物/kg_analyzer.py
🚀 启动增强版知识图谱分析器
支持的分析功能:
  ✅ 基础实体和关系统计     
  ✅ 建筑功能类别详情分析   
  ✅ 建筑形态类别详情分析   
  ✅ 土地利用类别详情分析 ⭐️
  ✅ POI类别详情分析 ⭐️     
  ✅ 建筑物详情分析 ⭐️      
  ✅ 关系模式和分组分析 ⭐️  
  ✅ 三元组模式分析 ⭐️新增  
  ✅ 实体类型交互分析 ⭐️新增
  ✅ 连通性指标分析 ⭐️      
  ✅ 增强版可视化图表 (3×3布局) ⭐️
  ✅ 详细分析报告生成 ⭐️
  ✅ 全面三元组分析报告 ⭐️新增
🚀 开始知识图谱完整分析 (增强版)
================================================================================
🔍 加载知识图谱三元组...
✅ 成功加载 107,119 个三元组
   实体数量: 36,046
   关系数量: 17

==================================================

📊 分类实体类型...

实体类型统计:
  POI            :   21,727 ( 60.3%)
  Building       :   13,746 ( 38.1%)
  Land           :      412 (  1.1%)
  Region         :      122 (  0.3%)
  Category       :       14 (  0.0%)
  Morphology     :       11 (  0.0%)
  Function       :        6 (  0.0%)
  BusinessCircle :        5 (  0.0%)
  LandUse        :        3 (  0.0%)

==================================================

🔗 分析关系统计...

关系类型统计:
  cateOf                   :  21,727 ( 20.3%)
  locateAt                 :  21,727 ( 20.3%)
  hasFunctionBuilding      :  13,746 ( 12.8%)
  withinRegion             :  12,824 ( 12.0%)
  belongsToBuilding        :  11,985 ( 11.2%)
  connectedTo              :   9,132 (  8.5%)
  similarFunction          :   6,774 (  6.3%)
  flowTransition           :   4,928 (  4.6%)
  nearBy                   :   1,383 (  1.3%)
  highConvenience          :     636 (  0.6%)
  borderBy                 :     580 (  0.5%)
  belongsToLand            :     412 (  0.4%)
  hasLandUse               :     412 (  0.4%)
  hasMorphology            :     412 (  0.4%)
  morphologySimilar        :     331 (  0.3%)
  provideService           :      55 (  0.1%)
  densityInfluences        :      55 (  0.1%)

==================================================

🎯 分析尾实体类型分布...

尾实体类型分布:
  Land           :    378 种 (出现  12,316 次)
  Category       :     14 种 (出现  21,727 次)
  Region         :    122 种 (出现  49,374 次)
  Building       :   2168 种 (出现   9,132 次)
  Function       :      6 种 (出现  13,746 次)
  LandUse        :      3 种 (出现     412 次)
  Morphology     :     11 种 (出现     412 次)

各关系的尾实体类型:

  📌 belongsToBuilding:
    → Land           : 11,985 (100.0%)

  📌 belongsToLand:
    → Region         :    412 (100.0%)

  📌 borderBy:
    → Region         :    580 (100.0%)

  📌 cateOf:
    → Category       : 21,727 (100.0%)

  📌 connectedTo:
    → Building       :  9,132 (100.0%)

  📌 densityInfluences:
    → Region         :     55 (100.0%)

  📌 flowTransition:
    → Region         :  4,928 (100.0%)

  📌 hasFunctionBuilding:
    → Function       : 13,746 (100.0%)

  📌 hasLandUse:
    → LandUse        :    412 (100.0%)

  📌 hasMorphology:
    → Morphology     :    412 (100.0%)

  📌 highConvenience:
    → Region         :    636 (100.0%)

  📌 locateAt:
    → Region         : 21,727 (100.0%)

  📌 morphologySimilar:
    → Land           :    331 (100.0%)

  📌 nearBy:
    → Region         :  1,383 (100.0%)

  📌 provideService:
    → Region         :     55 (100.0%)

  📌 similarFunction:
    → Region         :  6,774 (100.0%)

  📌 withinRegion:
    → Region         : 12,824 (100.0%)

==================================================

🏗️ 详细分析功能和形态类别...

功能类别详情 (共 6 种):
  Func_Residential          (住宅      ): 10489 次 ( 76.3%)
  Func_Other                (其他      ):   996 次 (  7.2%)
  Func_Public               (公共      ):   805 次 (  5.9%)
  Func_Office               (办公      ):   769 次 (  5.6%)
  Func_Commercial           (商业      ):   686 次 (  5.0%)
  Func_Industrial           (工业      ):     1 次 (  0.0%)

形态类别详情 (共 11 种):
  Morph_MidRiseMidDensity        (中层中密度       ):   173 次 ( 42.0%)
  Morph_Vacant                   (空地          ):    70 次 ( 17.0%)
  Morph_MidRiseHighDensity       (中层高密度       ):    55 次 ( 13.3%)
  Morph_MidRiseLowDensity        (中层低密度       ):    45 次 ( 10.9%)
  Morph_LowRiseMidDensity        (低层中密度       ):    26 次 (  6.3%)
  Morph_HighRiseLowDensity       (高层低密度       ):    25 次 (  6.1%)
  Morph_LowRiseLowDensity        (低层低密度       ):     7 次 (  1.7%)
  Morph_LowRiseHighDensity       (低层高密度       ):     5 次 (  1.2%)
  Morph_HighRiseMidDensity       (高层中密度       ):     4 次 (  1.0%)
  Morph_SuperHighRise            (Morph_SuperHighRise):     1 次 (  0.2%)
  Morph_HighRiseHighDensity      (高层高密度       ):     1 次 (  0.2%)

==================================================

🗺️ 分析土地利用类别详情...

土地利用类别详情 (共 3 种):
  LandUse_Residential       (居住用地        ):   220 次,  220 地块 ( 53.4%)
  LandUse_Other             (其他用地        ):   187 次,  187 地块 ( 45.4%)
  LandUse_Industrial        (工业用地        ):     5 次,    5 地块 (  1.2%)

==================================================

🏪 分析POI类别详情...

POI类别详情 (共 14 种):
  Cate_2               (Cate_2      ):  4264 次,  4264 POI ( 19.6%)
  Cate_5               (Cate_5      ):  3060 次,  3060 POI ( 14.1%)
  Cate_9               (Cate_9      ):  2368 次,  2368 POI ( 10.9%)
  Cate_7               (Cate_7      ):  2260 次,  2260 POI ( 10.4%)
  Cate_0               (Cate_0      ):  1800 次,  1800 POI (  8.3%)
  Cate_6               (Cate_6      ):  1714 次,  1714 POI (  7.9%)
  Cate_10              (Cate_10     ):  1627 次,  1627 POI (  7.5%)
  Cate_11              (Cate_11     ):  1039 次,  1039 POI (  4.8%)
  Cate_3               (Cate_3      ):   981 次,   981 POI (  4.5%)
  Cate_1               (Cate_1      ):   952 次,   952 POI (  4.4%)
  Cate_13              (Cate_13     ):   936 次,   936 POI (  4.3%)
  Cate_8               (Cate_8      ):   295 次,   295 POI (  1.4%)
  Cate_4               (Cate_4      ):   238 次,   238 POI (  1.1%)
  Cate_12              (Cate_12     ):   193 次,   193 POI (  0.9%)

==================================================

🏢 分析建筑物详情...

建筑物详情分析 (共 13746 个建筑):

  建筑功能分布:
    Func_Residential          (住宅      ): 10489 建筑 ( 76.3%)
    Func_Other                (其他      ):   996 建筑 (  7.2%)
    Func_Public               (公共      ):   805 建筑 (  5.9%)
    Func_Office               (办公      ):   769 建筑 (  5.6%)
    Func_Commercial           (商业      ):   686 建筑 (  5.0%)
    Func_Industrial           (工业      ):     1 建筑 (  0.0%)

  建筑形态分布 (通过地块关联):
    未发现建筑形态关系

  建筑空间分布:
    关联地块数: 0 个
    关联区域数: 112 个
    无地块关联数据

==================================================

🔗 分析关系模式...

关系分组统计:
  空间关系        :  45,646 ( 42.6%)
  功能关系        :  42,659 ( 39.8%)
  归属关系        :  12,397 ( 11.6%)
  移动关系        :   4,983 (  4.7%)
  形态关系        :     743 (  0.7%)
  便利关系        :     636 (  0.6%)
  服务关系        :      55 (  0.1%)

主要关系的实体类型模式:

  📌 cateOf (21,727 次):
    POI->Category       : 21,727 (100.0%)

  📌 locateAt (21,727 次):
    POI->Region         : 21,727 (100.0%)

  📌 hasFunctionBuilding (13,746 次):
    Building->Function  : 13,746 (100.0%)

  📌 withinRegion (12,824 次):
    Building->Region    : 12,824 (100.0%)

  📌 belongsToBuilding (11,985 次):
    Building->Land      : 11,985 (100.0%)

  📌 connectedTo (9,132 次):
    Building->Building  :  9,132 (100.0%)

  📌 similarFunction (6,774 次):
    Region->Region      :  6,774 (100.0%)

  📌 flowTransition (4,928 次):
    Region->Region      :  4,928 (100.0%)

  📌 nearBy (1,383 次):
    Region->Region      :  1,383 (100.0%)

  📌 highConvenience (636 次):
    Region->Region      :    636 (100.0%)

==================================================

🔍 分析所有三元组的种类和模式...

📋 三元组模式统计 (共 17 种模式):
================================================================================

 1. POI --cateOf--> Category
    数量: 21,727 个 (20.28%)
    示例:
      1. (POI_174720, cateOf, Cate_2)
      2. (POI_168840, cateOf, Cate_2)
      3. (POI_143104, cateOf, Cate_13)

 2. POI --locateAt--> Region
    数量: 21,727 个 (20.28%)
    示例:
      1. (POI_97432, locateAt, Region_598)
      2. (POI_172671, locateAt, Region_605)
      3. (POI_128057, locateAt, Region_224)

 3. Building --hasFunctionBuilding--> Function
    数量: 13,746 个 (12.83%)
    示例:
      1. (Building_47012, hasFunctionBuilding, Func_Residential)
      2. (Building_50431, hasFunctionBuilding, Func_Residential)
      3. (Building_78418, hasFunctionBuilding, Func_Residential)

 4. Building --withinRegion--> Region
    数量: 12,824 个 (11.97%)
    示例:
      1. (Building_67143, withinRegion, Region_597)
      2. (Building_69932, withinRegion, Region_595)
      3. (Building_82527, withinRegion, Region_697)

 5. Building --belongsToBuilding--> Land
    数量: 11,985 个 (11.19%)
    示例:
      1. (Building_63467, belongsToBuilding, Land_1825)
      2. (Building_51817, belongsToBuilding, Land_680)
      3. (Building_85083, belongsToBuilding, Land_2057)

 6. Building --connectedTo--> Building
    数量: 9,132 个 (8.53%)
    示例:
      1. (Building_61562, connectedTo, Building_61566)
      2. (Building_46939, connectedTo, Building_46942)
      3. (Building_52598, connectedTo, Building_53473)

 7. Region --similarFunction--> Region
    数量: 6,774 个 (6.32%)
    示例:
      1. (Region_197, similarFunction, Region_686)
      2. (Region_600, similarFunction, Region_640)
      3. (Region_225, similarFunction, Region_667)

 8. Region --flowTransition--> Region
    数量: 4,928 个 (4.60%)
    示例:
      1. (Region_644, flowTransition, Region_634)
      2. (Region_683, flowTransition, Region_225)
      3. (Region_667, flowTransition, Region_635)

 9. Region --nearBy--> Region
    数量: 1,383 个 (1.29%)
    示例:
      1. (Region_229, nearBy, Region_681)
      2. (Region_222, nearBy, Region_640)
      3. (Region_230, nearBy, Region_682)

10. Region --highConvenience--> Region
    数量: 636 个 (0.59%)
    示例:
      1. (Region_210, highConvenience, Region_212)
      2. (Region_219, highConvenience, Region_640)
      3. (Region_227, highConvenience, Region_643)

11. Region --borderBy--> Region
    数量: 580 个 (0.54%)
    示例:
      1. (Region_210, borderBy, Region_635)
      2. (Region_220, borderBy, Region_636)
      3. (Region_669, borderBy, Region_685)

12. Land --belongsToLand--> Region
    数量: 412 个 (0.38%)
    示例:
      1. (Land_748, belongsToLand, Region_192)
      2. (Land_1819, belongsToLand, Region_203)
      3. (Land_1069, belongsToLand, Region_690)

13. Land --hasLandUse--> LandUse
    数量: 412 个 (0.38%)
    示例:
      1. (Land_811, hasLandUse, LandUse_Other)
      2. (Land_814, hasLandUse, LandUse_Residential)
      3. (Land_1869, hasLandUse, LandUse_Other)

14. Land --hasMorphology--> Morphology
    数量: 412 个 (0.38%)
    示例:
      1. (Land_770, hasMorphology, Morph_MidRiseMidDens...)
      2. (Land_679, hasMorphology, Morph_MidRiseMidDens...)
      3. (Land_1118, hasMorphology, Morph_MidRiseLowDens...)

15. Land --morphologySimilar--> Land
    数量: 331 个 (0.31%)
    示例:
      1. (Land_1816, morphologySimilar, Land_1819)
      2. (Land_1056, morphologySimilar, Land_1068)
      3. (Land_1033, morphologySimilar, Land_1118)

16. BusinessCircle --provideService--> Region
    数量: 55 个 (0.05%)
    示例:
      1. (BC_173, provideService, Region_662)
      2. (BC_175, provideService, Region_203)
      3. (BC_173, provideService, Region_647)

17. Region --densityInfluences--> Region
    数量: 55 个 (0.05%)
    示例:
      1. (Region_161, densityInfluences, Region_154)
      2. (Region_210, densityInfluences, Region_195)
      3. (Region_200, densityInfluences, Region_162)

🔗 各关系的实体类型模式:
================================================================================

📌 belongsToBuilding (总计: 11,985 个)
    Building->Land           : 11,985 (100.0%)

📌 belongsToLand (总计: 412 个)
    Land->Region             :    412 (100.0%)

📌 borderBy (总计: 580 个)
    Region->Region           :    580 (100.0%)

📌 cateOf (总计: 21,727 个)
    POI->Category            : 21,727 (100.0%)

📌 connectedTo (总计: 9,132 个)
    Building->Building       :  9,132 (100.0%)

📌 densityInfluences (总计: 55 个)
    Region->Region           :     55 (100.0%)

📌 flowTransition (总计: 4,928 个)
    Region->Region           :  4,928 (100.0%)

📌 hasFunctionBuilding (总计: 13,746 个)
    Building->Function       : 13,746 (100.0%)

📌 hasLandUse (总计: 412 个)
    Land->LandUse            :    412 (100.0%)

📌 hasMorphology (总计: 412 个)
    Land->Morphology         :    412 (100.0%)

📌 highConvenience (总计: 636 个)
    Region->Region           :    636 (100.0%)

📌 locateAt (总计: 21,727 个)
    POI->Region              : 21,727 (100.0%)

📌 morphologySimilar (总计: 331 个)
    Land->Land               :    331 (100.0%)

📌 nearBy (总计: 1,383 个)
    Region->Region           :  1,383 (100.0%)

📌 provideService (总计: 55 个)
    BusinessCircle->Region   :     55 (100.0%)

📌 similarFunction (总计: 6,774 个)
    Region->Region           :  6,774 (100.0%)

📌 withinRegion (总计: 12,824 个)
    Building->Region         : 12,824 (100.0%)

==================================================

🌐 分析实体类型间的交互模式...

📊 实体类型交互矩阵:
================================================================================
源类型              Building  Business  Category  Function      Land   LandUse  Morpholo       POI    Region
---------------------------------------------------------------------------------------------------------
Building            9,132                        13,746    11,985                                  12,824
BusinessCircle                                                                                         55
Category
Function
Land                                                          331       412       412                 412
LandUse
Morphology
POI                                    21,727                                                      21,727
Region                                                                                             14,356

🔍 主要实体类型交互详情:
================================================================================

 1. POI → Category
    连接数: 21,727 (20.28%)
    关系类型: cateOf

 2. POI → Region
    连接数: 21,727 (20.28%)
    关系类型: locateAt

 3. Region → Region
    连接数: 14,356 (13.40%)
    关系类型: borderBy, densityInfluences, flowTransition, highConvenience, nearBy, similarFunction

 4. Building → Function
    连接数: 13,746 (12.83%)
    关系类型: hasFunctionBuilding

 5. Building → Region
    连接数: 12,824 (11.97%)
    关系类型: withinRegion

 6. Building → Land
    连接数: 11,985 (11.19%)
    关系类型: belongsToBuilding

 7. Building → Building
    连接数: 9,132 (8.53%)
    关系类型: connectedTo

 8. Land → Region
    连接数: 412 (0.38%)
    关系类型: belongsToLand

 9. Land → LandUse
    连接数: 412 (0.38%)
    关系类型: hasLandUse

10. Land → Morphology
    连接数: 412 (0.38%)
    关系类型: hasMorphology

11. Land → Land
    连接数: 331 (0.31%)
    关系类型: morphologySimilar

12. BusinessCircle → Region
    连接数: 55 (0.05%)
    关系类型: provideService

==================================================

📊 分析连通性指标...

连通性统计:
  总实体数: 36,046
  总连接数: 107,119
  平均连接度: 5.94
  图密度: 0.000165

连接度最高的实体 (Top 10):
  Func_Residential          (Function    ): 10489 连接
  Cate_2                    (Category    ): 4264 连接
  Cate_5                    (Category    ): 3060 连接
  Cate_9                    (Category    ): 2368 连接
  Cate_7                    (Category    ): 2260 连接
  Region_697                (Region      ): 2202 连接
  Region_606                (Region      ): 1848 连接
  Cate_0                    (Category    ): 1800 连接
  Cate_6                    (Category    ): 1714 连接
  Cate_10                   (Category    ): 1627 连接

各实体类型平均连接度:
  Building       : 平均    4.1, 最大   12, 最小    1
  BusinessCircle : 平均   11.0, 最大   25, 最小    2
  Category       : 平均 1551.9, 最大 4264, 最小  193
  Function       : 平均 2291.0, 最大 10489, 最小    1
  Land           : 平均   33.7, 最大  820, 最小    3
  LandUse        : 平均  137.3, 最大  220, 最小    5
  Morphology     : 平均   37.5, 最大  173, 最小    1
  POI            : 平均    2.0, 最大    2, 最小    2
  Region         : 平均  522.4, 最大 2202, 最小   16

==================================================

📋 三元组样本展示 (前 50 个):

头实体                  关系                    尾实体
--------------------------------------------------------------------------------
Building_63467                  belongsToBuilding       Land_1825
POI_174720                      cateOf                  Cate_2
POI_168840                      cateOf                  Cate_2
Region_229                      nearBy                  Region_681
POI_97432                       locateAt                Region_598
Region_197                      similarFunction         Region_686
Building_61562                  connectedTo             Building_61566
POI_143104                      cateOf                  Cate_13
POI_172671                      locateAt                Region_605
POI_128057                      locateAt                Region_224
--------------------------------------------------------------------------------
POI_174940                      locateAt                Region_606
Region_600                      similarFunction         Region_640
Building_51817                  belongsToBuilding       Land_680
Building_85083                  belongsToBuilding       Land_2057
POI_73109                       locateAt                Region_608
Building_67143                  withinRegion            Region_597
POI_49129                       cateOf                  Cate_5
Building_81320                  belongsToBuilding       Land_1860
Building_47012                  hasFunctionBuilding     Func_Residential
POI_169281                      locateAt                Region_608
--------------------------------------------------------------------------------
POI_172268                      locateAt                Region_206
POI_138852                      locateAt                Region_669
Land_748                        belongsToLand           Region_192
Building_50431                  hasFunctionBuilding     Func_Residential
Building_78418                  hasFunctionBuilding     Func_Residential
POI_12668                       locateAt                Region_601
Building_73568                  belongsToBuilding       Land_1765
Building_82463                  belongsToBuilding       Land_1968
Building_59073                  hasFunctionBuilding     Func_Residential
Land_811                        hasLandUse              LandUse_Other
--------------------------------------------------------------------------------
POI_163201                      locateAt                Region_228
Building_46939                  connectedTo             Building_46942
Building_83189                  hasFunctionBuilding     Func_Residential
Land_770                        hasMorphology           Morph_MidRiseMidDens...
POI_47050                       locateAt                Region_666
Building_69932                  withinRegion            Region_595
Region_225                      similarFunction         Region_667
Building_87066                  hasFunctionBuilding     Func_Residential
POI_172373                      cateOf                  Cate_2
Land_679                        hasMorphology           Morph_MidRiseMidDens...
--------------------------------------------------------------------------------
POI_92706                       locateAt                Region_681
Building_80522                  hasFunctionBuilding     Func_Residential
Building_82527                  withinRegion            Region_697
Building_86757                  hasFunctionBuilding     Func_Public
POI_108873                      cateOf                  Cate_9
POI_92662                       locateAt                Region_690
POI_111535                      locateAt                Region_196
Building_52598                  connectedTo             Building_53473
POI_37291                       locateAt                Region_163
POI_111525                      locateAt                Region_195

==================================================

📈 生成增强版可视化图表...
✅ 增强版可视化图表已保存: D:\研二\能耗估算\666-模型对比项目\KG\建筑物\enhanced_analysis_results\enhanced_kg_analysis_visualization.png

==================================================
💾 导出增强版分析结果...

💾 导出分析结果...
✅ 分析结果已导出到: D:\研二\能耗估算\666-模型对比项目\KG\建筑物\enhanced_analysis_results
   📄 basic_statistics.json - 基本统计信息
   📄 entity_details.json - 实体类型详情
   📄 function_morphology_details.json - 功能形态详情
   📄 sample_triples.csv - 三元组样本
   📄 tail_entity_statistics.csv - 尾实体统计
✅ 增强版分析结果已导出到: D:\研二\能耗估算\666-模型对比项目\KG\建筑物\enhanced_analysis_results
   📄 landuse_details.json - 土地利用详情
   📄 poi_category_details.json - POI类别详情
   📄 building_analysis.json - 建筑物分析
   📄 relation_patterns.json - 关系模式分析
   📄 connectivity_analysis.json - 连通性分析

==================================================
📝 生成详细分析报告...
✅ 详细分析报告已保存: D:\研二\能耗估算\666-模型对比项目\KG\建筑物\enhanced_analysis_results\kg_analysis_report_20250604_002005.txt

==================================================

📝 生成全面的三元组分析报告...

🔍 分析所有三元组的种类和模式...

📋 三元组模式统计 (共 17 种模式):
================================================================================

 1. POI --cateOf--> Category
    数量: 21,727 个 (20.28%)
    示例:
      1. (POI_174720, cateOf, Cate_2)
      2. (POI_168840, cateOf, Cate_2)
      3. (POI_143104, cateOf, Cate_13)

 2. POI --locateAt--> Region
    数量: 21,727 个 (20.28%)
    示例:
      1. (POI_97432, locateAt, Region_598)
      2. (POI_172671, locateAt, Region_605)
      3. (POI_128057, locateAt, Region_224)

 3. Building --hasFunctionBuilding--> Function
    数量: 13,746 个 (12.83%)
    示例:
      1. (Building_47012, hasFunctionBuilding, Func_Residential)
      2. (Building_50431, hasFunctionBuilding, Func_Residential)
      3. (Building_78418, hasFunctionBuilding, Func_Residential)

 4. Building --withinRegion--> Region
    数量: 12,824 个 (11.97%)
    示例:
      1. (Building_67143, withinRegion, Region_597)
      2. (Building_69932, withinRegion, Region_595)
      3. (Building_82527, withinRegion, Region_697)

 5. Building --belongsToBuilding--> Land
    数量: 11,985 个 (11.19%)
    示例:
      1. (Building_63467, belongsToBuilding, Land_1825)
      2. (Building_51817, belongsToBuilding, Land_680)
      3. (Building_85083, belongsToBuilding, Land_2057)

 6. Building --connectedTo--> Building
    数量: 9,132 个 (8.53%)
    示例:
      1. (Building_61562, connectedTo, Building_61566)
      2. (Building_46939, connectedTo, Building_46942)
      3. (Building_52598, connectedTo, Building_53473)

 7. Region --similarFunction--> Region
    数量: 6,774 个 (6.32%)
    示例:
      1. (Region_197, similarFunction, Region_686)
      2. (Region_600, similarFunction, Region_640)
      3. (Region_225, similarFunction, Region_667)

 8. Region --flowTransition--> Region
    数量: 4,928 个 (4.60%)
    示例:
      1. (Region_644, flowTransition, Region_634)
      2. (Region_683, flowTransition, Region_225)
      3. (Region_667, flowTransition, Region_635)

 9. Region --nearBy--> Region
    数量: 1,383 个 (1.29%)
    示例:
      1. (Region_229, nearBy, Region_681)
      2. (Region_222, nearBy, Region_640)
      3. (Region_230, nearBy, Region_682)

10. Region --highConvenience--> Region
    数量: 636 个 (0.59%)
    示例:
      1. (Region_210, highConvenience, Region_212)
      2. (Region_219, highConvenience, Region_640)
      3. (Region_227, highConvenience, Region_643)

11. Region --borderBy--> Region
    数量: 580 个 (0.54%)
    示例:
      1. (Region_210, borderBy, Region_635)
      2. (Region_220, borderBy, Region_636)
      3. (Region_669, borderBy, Region_685)

12. Land --belongsToLand--> Region
    数量: 412 个 (0.38%)
    示例:
      1. (Land_748, belongsToLand, Region_192)
      2. (Land_1819, belongsToLand, Region_203)
      3. (Land_1069, belongsToLand, Region_690)

13. Land --hasLandUse--> LandUse
    数量: 412 个 (0.38%)
    示例:
      1. (Land_811, hasLandUse, LandUse_Other)
      2. (Land_814, hasLandUse, LandUse_Residential)
      3. (Land_1869, hasLandUse, LandUse_Other)

14. Land --hasMorphology--> Morphology
    数量: 412 个 (0.38%)
    示例:
      1. (Land_770, hasMorphology, Morph_MidRiseMidDens...)
      2. (Land_679, hasMorphology, Morph_MidRiseMidDens...)
      3. (Land_1118, hasMorphology, Morph_MidRiseLowDens...)

15. Land --morphologySimilar--> Land
    数量: 331 个 (0.31%)
    示例:
      1. (Land_1816, morphologySimilar, Land_1819)
      2. (Land_1056, morphologySimilar, Land_1068)
      3. (Land_1033, morphologySimilar, Land_1118)

16. BusinessCircle --provideService--> Region
    数量: 55 个 (0.05%)
    示例:
      1. (BC_173, provideService, Region_662)
      2. (BC_175, provideService, Region_203)
      3. (BC_173, provideService, Region_647)

17. Region --densityInfluences--> Region
    数量: 55 个 (0.05%)
    示例:
      1. (Region_161, densityInfluences, Region_154)
      2. (Region_210, densityInfluences, Region_195)
      3. (Region_200, densityInfluences, Region_162)

🔗 各关系的实体类型模式:
================================================================================

📌 belongsToBuilding (总计: 11,985 个)
    Building->Land           : 11,985 (100.0%)

📌 belongsToLand (总计: 412 个)
    Land->Region             :    412 (100.0%)

📌 borderBy (总计: 580 个)
    Region->Region           :    580 (100.0%)

📌 cateOf (总计: 21,727 个)
    POI->Category            : 21,727 (100.0%)

📌 connectedTo (总计: 9,132 个)
    Building->Building       :  9,132 (100.0%)

📌 densityInfluences (总计: 55 个)
    Region->Region           :     55 (100.0%)

📌 flowTransition (总计: 4,928 个)
    Region->Region           :  4,928 (100.0%)

📌 hasFunctionBuilding (总计: 13,746 个)
    Building->Function       : 13,746 (100.0%)

📌 hasLandUse (总计: 412 个)
    Land->LandUse            :    412 (100.0%)

📌 hasMorphology (总计: 412 个)
    Land->Morphology         :    412 (100.0%)

📌 highConvenience (总计: 636 个)
    Region->Region           :    636 (100.0%)

📌 locateAt (总计: 21,727 个)
    POI->Region              : 21,727 (100.0%)

📌 morphologySimilar (总计: 331 个)
    Land->Land               :    331 (100.0%)

📌 nearBy (总计: 1,383 个)
    Region->Region           :  1,383 (100.0%)

📌 provideService (总计: 55 个)
    BusinessCircle->Region   :     55 (100.0%)

📌 similarFunction (总计: 6,774 个)
    Region->Region           :  6,774 (100.0%)

📌 withinRegion (总计: 12,824 个)
    Building->Region         : 12,824 (100.0%)

🔗 分析关系模式...

关系分组统计:
  空间关系        :  45,646 ( 42.6%)
  功能关系        :  42,659 ( 39.8%)
  归属关系        :  12,397 ( 11.6%)
  移动关系        :   4,983 (  4.7%)
  形态关系        :     743 (  0.7%)
  便利关系        :     636 (  0.6%)
  服务关系        :      55 (  0.1%)

主要关系的实体类型模式:

  📌 cateOf (21,727 次):
    POI->Category       : 21,727 (100.0%)

  📌 locateAt (21,727 次):
    POI->Region         : 21,727 (100.0%)

  📌 hasFunctionBuilding (13,746 次):
    Building->Function  : 13,746 (100.0%)

  📌 withinRegion (12,824 次):
    Building->Region    : 12,824 (100.0%)

  📌 belongsToBuilding (11,985 次):
    Building->Land      : 11,985 (100.0%)

  📌 connectedTo (9,132 次):
    Building->Building  :  9,132 (100.0%)

  📌 similarFunction (6,774 次):
    Region->Region      :  6,774 (100.0%)

  📌 flowTransition (4,928 次):
    Region->Region      :  4,928 (100.0%)

  📌 nearBy (1,383 次):
    Region->Region      :  1,383 (100.0%)

  📌 highConvenience (636 次):
    Region->Region      :    636 (100.0%)

🌐 分析实体类型间的交互模式...

📊 实体类型交互矩阵:
================================================================================
源类型              Building  Business  Category  Function      Land   LandUse  Morpholo       POI    Region
---------------------------------------------------------------------------------------------------------
Building            9,132                        13,746    11,985                                  12,824
BusinessCircle                                                                                         55
Category
Function
Land                                                          331       412       412                 412
LandUse
Morphology
POI                                    21,727                                                      21,727
Region                                                                                             14,356

🔍 主要实体类型交互详情:
================================================================================

 1. POI → Category
    连接数: 21,727 (20.28%)
    关系类型: cateOf

 2. POI → Region
    连接数: 21,727 (20.28%)
    关系类型: locateAt

 3. Region → Region
    连接数: 14,356 (13.40%)
    关系类型: borderBy, densityInfluences, flowTransition, highConvenience, nearBy, similarFunction

 4. Building → Function
    连接数: 13,746 (12.83%)
    关系类型: hasFunctionBuilding

 5. Building → Region
    连接数: 12,824 (11.97%)
    关系类型: withinRegion

 6. Building → Land
    连接数: 11,985 (11.19%)
    关系类型: belongsToBuilding

 7. Building → Building
    连接数: 9,132 (8.53%)
    关系类型: connectedTo

 8. Land → Region
    连接数: 412 (0.38%)
    关系类型: belongsToLand

 9. Land → LandUse
    连接数: 412 (0.38%)
    关系类型: hasLandUse

10. Land → Morphology
    连接数: 412 (0.38%)
    关系类型: hasMorphology

11. Land → Land
    连接数: 331 (0.31%)
    关系类型: morphologySimilar

12. BusinessCircle → Region
    连接数: 55 (0.05%)
    关系类型: provideService
✅ 全面三元组分析报告已保存: D:\研二\能耗估算\666-模型对比项目\KG\建筑物\enhanced_analysis_results\comprehensive_triple_analysis_20250604_002005.txt

================================================================================
🎉 知识图谱增强版分析完成！
⏱️ 总耗时: 11.3 秒

📋 增强版分析总结:
   三元组总数: 107,119
   实体总数: 36,046
   关系类型数: 17
   实体类型数: 9

🎯 详细类别统计:
   建筑功能类别: 6 种
   建筑形态类别: 11 种
   土地利用类别: 3 种 ⭐️
   POI类别: 14 种 ⭐️
   建筑物实体: 13,746 个 ⭐️

   🏢 建筑功能类别:
     Func_Commercial (商业)
     Func_Industrial (工业)
     Func_Office (办公)
     Func_Other (其他)
     Func_Public (公共)
     Func_Residential (住宅)

   🗺️ 土地利用类别:
     LandUse_Industrial (工业用地)
     LandUse_Other (其他用地)
     LandUse_Residential (居住用地)

   🏗️ 建筑形态类别:
     Morph_HighRiseHighDensity (高层高密度)
     Morph_HighRiseLowDensity (高层低密度)
     Morph_HighRiseMidDensity (高层中密度)
     Morph_LowRiseHighDensity (低层高密度)
     Morph_LowRiseLowDensity (低层低密度)
     Morph_LowRiseMidDensity (低层中密度)
     Morph_MidRiseHighDensity (中层高密度)
     Morph_MidRiseLowDensity (中层低密度)
     Morph_MidRiseMidDensity (中层中密度)
     Morph_SuperHighRise (Morph_SuperHighRise)
     Morph_Vacant (空地)

   🏪 POI类别 (前10):
     Cate_0 (Cate_0)
     Cate_1 (Cate_1)
     Cate_10 (Cate_10)
     Cate_11 (Cate_11)
     Cate_12 (Cate_12)
     Cate_13 (Cate_13)
     Cate_2 (Cate_2)
     Cate_3 (Cate_3)
     Cate_4 (Cate_4)
     Cate_5 (Cate_5)
     ... 还有 4 个类别

📊 连通性总结:
   平均连接度: 5.94
   最大连接度: 10489
   图密度: 0.000165

🎉 分析完成！请查看输出目录: D:\研二\能耗估算\666-模型对比项目\KG\建筑物\enhanced_analysis_results
   📊 enhanced_kg_analysis_visualization.png - 增强版可视化图表
   📝 kg_analysis_report_*.txt - 详细分析报告
   📄 *.json - 各类详细分析数据
PS D:\研二\能耗估算\666-模型对比项目\KG> 