"""
诊断脚本：检查地块关系数量不匹配问题
"""

import pandas as pd
import geopandas as gpd
from pathlib import Path

def diagnose_land_relations():
    """诊断地块关系数量不匹配问题"""
    
    print("🔍 诊断地块关系数量不匹配问题")
    print("="*60)
    
    # 数据文件路径（请根据实际情况调整）
    data_paths = {
        'l5_gdf': r"D:\研二\能耗估算\666-模型对比项目\KG\三环\有建筑物\data\L5_with_building_info.geojson",
        'l4_gdf': r"D:\研二\能耗估算\666-模型对比项目\KG\三环\有建筑物\data\L4_with_poi_info.geojson",
        'landuse_gdf': r"D:\研二\能耗估算\666-模型对比项目\KG\三环\有建筑物\data\landuse_with_info.geojson",
        'kg_file': r"D:\研二\能耗估算\666-模型对比项目\KG\建筑物\kg_with_building_fixed.txt"
    }
    
    # 1. 检查L5地块数据
    print("1. 检查L5地块数据...")
    try:
        l5_gdf = gpd.read_file(data_paths['l5_gdf'])
        print(f"   L5地块总数: {len(l5_gdf)}")
        
        # 检查region_id字段
        if 'region_id' in l5_gdf.columns:
            valid_region_ids = l5_gdf['region_id'].notna().sum()
            print(f"   有效region_id数量: {valid_region_ids}")
            print(f"   缺失region_id数量: {len(l5_gdf) - valid_region_ids}")
            
            if valid_region_ids < len(l5_gdf):
                print("   ⚠️ 发现缺失region_id的地块")
                missing_region = l5_gdf[l5_gdf['region_id'].isna()]
                print(f"   缺失region_id的地块示例: {missing_region['land_id'].head().tolist()}")
        else:
            print("   ❌ 未找到region_id字段")
        
        # 检查landuse_type_id字段
        if 'landuse_type_id' in l5_gdf.columns:
            valid_landuse_ids = l5_gdf['landuse_type_id'].notna().sum()
            print(f"   有效landuse_type_id数量: {valid_landuse_ids}")
            print(f"   缺失landuse_type_id数量: {len(l5_gdf) - valid_landuse_ids}")
            
            if valid_landuse_ids < len(l5_gdf):
                print("   ⚠️ 发现缺失landuse_type_id的地块")
                missing_landuse = l5_gdf[l5_gdf['landuse_type_id'].isna()]
                print(f"   缺失landuse_type_id的地块示例: {missing_landuse['land_id'].head().tolist()}")
        else:
            print("   ❌ 未找到landuse_type_id字段")
            
        # 显示字段信息
        print(f"   L5地块字段: {list(l5_gdf.columns)}")
        
    except Exception as e:
        print(f"   ❌ 读取L5地块数据失败: {e}")
        return
    
    # 2. 检查知识图谱文件中的实际关系数量
    print(f"\n2. 检查知识图谱文件中的实际关系数量...")
    try:
        belongsToLand_count = 0
        hasLandUse_count = 0
        land_entities = set()
        
        with open(data_paths['kg_file'], 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                parts = line.split('\t')
                if len(parts) != 3:
                    continue
                
                head, relation, tail = parts
                
                if relation == 'belongsToLand':
                    belongsToLand_count += 1
                    if head.startswith('Land_'):
                        land_entities.add(head)
                
                elif relation == 'hasLandUse':
                    hasLandUse_count += 1
                    if head.startswith('Land_'):
                        land_entities.add(head)
        
        print(f"   知识图谱中belongsToLand关系数量: {belongsToLand_count}")
        print(f"   知识图谱中hasLandUse关系数量: {hasLandUse_count}")
        print(f"   涉及的地块实体数量: {len(land_entities)}")
        
    except Exception as e:
        print(f"   ❌ 读取知识图谱文件失败: {e}")
        return
    
    # 3. 对比分析
    print(f"\n3. 对比分析...")
    print(f"   L5地块总数: {len(l5_gdf)}")
    print(f"   belongsToLand关系数量: {belongsToLand_count}")
    print(f"   hasLandUse关系数量: {hasLandUse_count}")
    
    # 计算缺失比例
    if len(l5_gdf) > 0:
        belongsToLand_coverage = belongsToLand_count / len(l5_gdf) * 100
        hasLandUse_coverage = hasLandUse_count / len(l5_gdf) * 100
        
        print(f"   belongsToLand覆盖率: {belongsToLand_coverage:.1f}%")
        print(f"   hasLandUse覆盖率: {hasLandUse_coverage:.1f}%")
        
        if belongsToLand_coverage < 95:
            print("   ⚠️ belongsToLand覆盖率过低，可能存在数据质量问题")
        
        if hasLandUse_coverage < 95:
            print("   ⚠️ hasLandUse覆盖率过低，可能存在数据质量问题")
    
    # 4. 检查具体的数据质量问题
    print(f"\n4. 检查数据质量问题...")
    
    # 检查region_id的有效性
    if 'region_id' in l5_gdf.columns:
        unique_regions = l5_gdf['region_id'].dropna().unique()
        print(f"   L5地块关联的区域数量: {len(unique_regions)}")
        print(f"   区域ID示例: {unique_regions[:5].tolist()}")
    
    # 检查landuse_type_id的有效性
    if 'landuse_type_id' in l5_gdf.columns:
        unique_landuses = l5_gdf['landuse_type_id'].dropna().unique()
        print(f"   L5地块关联的土地利用类型数量: {len(unique_landuses)}")
        print(f"   土地利用类型示例: {unique_landuses[:5].tolist()}")
    
    # 5. 提供修复建议
    print(f"\n5. 修复建议...")
    
    if belongsToLand_count < len(l5_gdf) * 0.95:
        print("   📋 belongsToLand关系修复建议:")
        print("     - 检查L5地块的region_id字段是否完整")
        print("     - 确认空间关联逻辑是否正确")
        print("     - 检查L4区域数据是否完整")
    
    if hasLandUse_count < len(l5_gdf) * 0.95:
        print("   📋 hasLandUse关系修复建议:")
        print("     - 检查L5地块的landuse_type_id字段是否完整")
        print("     - 确认土地利用数据关联逻辑是否正确")
        print("     - 检查土地利用分类数据是否完整")
    
    print(f"\n6. 理论期望值...")
    print(f"   理论上每个地块都应该有:")
    print(f"     - 1个belongsToLand关系 (地块→区域)")
    print(f"     - 1个hasLandUse关系 (地块→土地利用类型)")
    print(f"   因此期望关系数量应该接近地块总数: {len(l5_gdf)}")

def check_data_generation_logic():
    """检查数据生成逻辑"""
    print(f"\n" + "="*60)
    print("🔧 检查数据生成逻辑...")
    
    print(f"\n根据代码逻辑，关系生成的条件是:")
    print(f"1. belongsToLand关系:")
    print(f"   - 条件: pd.notna(land.get('region_id'))")
    print(f"   - 如果region_id为空值，则不会生成关系")
    
    print(f"\n2. hasLandUse关系:")
    print(f"   - 条件: pd.notna(land.get('landuse_type_id'))")
    print(f"   - 如果landuse_type_id为空值，则不会生成关系")
    
    print(f"\n💡 可能的解决方案:")
    print(f"1. 数据预处理阶段确保字段完整性")
    print(f"2. 添加数据质量检查和修复逻辑")
    print(f"3. 对缺失值进行合理的默认值填充")
    print(f"4. 增加数据生成过程中的质量监控")

if __name__ == "__main__":
    diagnose_land_relations()
    check_data_generation_logic()
    
    print(f"\n" + "="*60)
    print("🎯 总结:")
    print("数量不匹配的主要原因可能是:")
    print("1. 源数据中region_id或landuse_type_id字段存在缺失值")
    print("2. 空间关联过程中部分地块未能成功关联到区域或土地利用类型")
    print("3. 数据预处理阶段的质量控制不够严格")
    print("\n建议运行此诊断脚本来确定具体原因，然后针对性地修复数据质量问题。")
