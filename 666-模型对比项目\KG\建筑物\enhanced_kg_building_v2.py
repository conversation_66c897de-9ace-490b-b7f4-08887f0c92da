"""
增强版含建筑物知识图谱生成器 V2
独立运行版本，增强连通性策略

关系类型设计（15种）：
1. 空间关系 (3种)：
   - borderBy: 区域边界相接 + 缓冲区内 + 空间重叠
   - nearBy: 区域距离很近 + 区域间可达
   - locateAt: POI/建筑物位置关系

2. 功能相似性关系 (3种)：
   - similarFunction: 功能分布相似 + POI数量相似 + 类别多样性相似 + 主导功能相同 + 功能互补
   - highConvenience: 街区功能便利性强 (种类齐全)
   - functionalCluster: 功能聚类增强连接

3. 移动性关系 (2种)：
   - flowTransition: 实际人员流动 + 模拟流动 + 吸引流动
   - accessibilityFlow: 基于建筑物的可达性流动

4. 分类关系 (2种)：
   - cateOf: POI属于类别
   - belongTo: POI属于商圈

5. 服务关系 (1种)：
   - provideService: 商圈服务区域

6. 建筑物关系 (4种)：
   - belongsToBuilding: 建筑物属于L5地块
   - belongsToLand: L5地块属于L4区域
   - hasFunctionBuilding: 建筑物功能类型
   - hasMorphology: 地块建筑形态

增强连通性策略：
1. 多层次桥接：建筑物作为L4-L5-POI的桥梁
2. 功能聚类增强：识别功能密集区域建立额外连接
3. 距离梯度连接：不同距离阈值建立多种连接
4. 密度加权连接：基于建筑物密度增强连接
5. 形态相似连接：相似建筑形态之间的连接
"""

import os
import sys
import pandas as pd
import geopandas as gpd
import numpy as np
from shapely.geometry import Point
from scipy.spatial.distance import cosine
import itertools
from tqdm import tqdm
import time
from collections import defaultdict, Counter

# ==================== 配置部分 ====================

# 数据路径配置
DATA_PATHS = {
    "l4_shp_path": r"C:\Users\<USER>\Desktop\22\8-街区数据\沈河L4.shp",
    "l5_shp_path": r"C:\Users\<USER>\Desktop\22\8-街区数据\沈河L5.shp",
    "poi_path": r"C:\Users\<USER>\Desktop\22\6-POI数据\merged_poi.shp",
    "bc_path": r"C:\Users\<USER>\Desktop\22\15-商圈数据\沈阳商圈.shp",
    "checkin_path": r"D:\研二\能耗估算\1-沈阳\1-数据\6-微博数据\weibo1.csv",
    "building_path": r"C:\Users\<USER>\Desktop\22\9-建筑物数据\沈阳建筑物三环.shp",
    "land_use_path": r"C:\Users\<USER>\Desktop\22\14-地块数据\沈阳市.shp",
}

# 输出路径配置
OUTPUT_BASE_PATH = r"D:\研二\能耗估算\666-模型对比项目\KG\建筑物"
OUTPUT_PATHS = {
    "kg_with_building": os.path.join(OUTPUT_BASE_PATH, "kg_with_building_enhanced_v2.txt"),
    "category_mapping": os.path.join(OUTPUT_BASE_PATH, "category_mapping.csv"),
    "relation_stats": os.path.join(OUTPUT_BASE_PATH, "relation_statistics.csv"),
    "morphology_stats": os.path.join(OUTPUT_BASE_PATH, "morphology_statistics.csv"),
    "connectivity_analysis": os.path.join(OUTPUT_BASE_PATH, "connectivity_analysis.csv"),
}

# 增强连通性参数配置
PARAMS = {
    # === 空间关系参数 ===
    "nearby_distance": 300,        # nearBy关系距离阈值(米)
    "buffer_distance": 800,        # withinBuffer关系距离阈值(米)
    "overlap_threshold": 0.1,      # spatialOverlap重叠比例阈值
    "accessibility_min": 200,      # accessibleBy最小距离(米)
    "accessibility_max": 1500,     # accessibleBy最大距离(米)
    
    # === 相似性参数 ===
    "function_similarity_threshold": 0.35,   # 功能相似度阈值（降低增加连接）
    "poi_count_similarity_threshold": 0.5,   # POI数量相似度阈值（降低）
    "category_diversity_threshold": 0.6,     # 类别多样性相似度阈值（降低）
    
    # === 便利性参数 ===
    "convenience_min_categories": 6,         # 便利性最少POI类别数（降低）
    "convenience_min_pois": 15,              # 便利性最少POI总数（降低）
    "convenience_essential_categories": [     # 必需的基本服务类别
        "餐饮服务", "购物服务", "生活服务", "交通设施服务"
    ],
    "convenience_score_threshold": 10,       # 便利性得分门槛（降低）
    "convenience_distance_threshold": 1200,  # 便利性街区间最大距离（增加）
    
    # === 移动性参数 ===
    "flow_time_threshold": 7200,             # 流动时间阈值(秒)
    "flow_distance_threshold": 1200,         # 模拟流动距离阈值（增加）
    "attraction_distance_threshold": 2500,   # 吸引流动距离阈值（增加）
    
    # === 建筑物增强连通性参数 ===
    "building_nearby_distance": 100,         # 建筑物近邻距离(米)（增加）
    "building_function_threshold": 0.6,      # 建筑物功能相似度阈值（降低）
    "morphology_similarity_threshold": 0.7,  # 形态相似度阈值（降低）
    "building_cluster_distance": 150,        # 建筑物聚类距离（增加）
    "density_enhancement_threshold": 20,     # 密度增强阈值（建筑物数量）
    "multi_layer_bridge_distance": 50,      # 多层次桥接距离
    "functional_cluster_radius": 200,        # 功能聚类半径
    "gradient_distances": [100, 300, 600, 1000],  # 距离梯度连接
    
    # === Spacematrix形态分类阈值 ===
    "fsi_thresholds": [0.4, 0.8, 1.5, 2.5],  # 容积率阈值（调整）
    "gsi_thresholds": [0.15, 0.3, 0.45],     # 覆盖率阈值（调整）
    "osr_thresholds": [0.4, 0.8, 1.5],       # 开放空间率阈值（调整）
    "l_thresholds": [2, 4, 8],               # 层数阈值
    
    # === 坐标系统 ===
    "crs": "EPSG:4326",           # WGS84坐标系
    "utm_crs": "EPSG:32651",      # UTM 51N，适合沈阳地区
    
    # === ID前缀 ===
    "region_prefix": "Region_",
    "land_prefix": "Land_",
    "building_prefix": "Building_",
    "poi_prefix": "POI_",
    "category_prefix": "Cate_",
    "bc_prefix": "BC_",
    "morphology_prefix": "Morph_",
    "function_prefix": "Func_",
}

# 字段名映射
FIELD_MAPPING = {
    "l4": {
        "id_field": "BlockID",
        "geometry_field": "geometry",
    },
    "l5": {
        "id_field": "Id",
        "geometry_field": "geometry",
    },
    "poi": {
        "name_field": "name",
        "category_field": "main_cat",
        "subcategory_field": "sub_cat",
        "geometry_field": "geometry",
    },
    "bc": {
        "id_field": "OBJECTID",
        "name_field": "Name_CHN",
        "geometry_field": "geometry",
    },
    "checkin": {
        "user_field": "账号",
        "time_field": "发布时间",
        "lon_field": "经度",
        "lat_field": "纬度",
    },
    "building": {
        "id_field": "OBJECTID",
        "function_field": "main_use",
        "height_field": "height",
        "floor_field": "floor",
        "geometry_field": "geometry",
    },
    "land_use": {
        "type_field": "用地性质",
        "code_field": "用地代码",
        "geometry_field": "geometry",
    }
}

# 建筑物功能分类映射
BUILDING_FUNCTION_MAP = {
    "住宅": "Func_Residential",
    "商业": "Func_Commercial", 
    "办公": "Func_Office",
    "工业": "Func_Industrial",
    "公共": "Func_Public",
    "教育": "Func_Education",
    "医疗": "Func_Medical",
    "文化": "Func_Cultural",
    "体育": "Func_Sports",
    "交通": "Func_Transport",
    "其他": "Func_Other",
}

# 形态类型
MORPHOLOGY_TYPES = [
    "Morph_LowRiseLowDensity",      # 低层低密度
    "Morph_LowRiseMidDensity",      # 低层中密度
    "Morph_LowRiseHighDensity",     # 低层高密度
    "Morph_MidRiseLowDensity",      # 中层低密度
    "Morph_MidRiseMidDensity",      # 中层中密度
    "Morph_MidRiseHighDensity",     # 中层高密度
    "Morph_HighRiseLowDensity",     # 高层低密度
    "Morph_HighRiseMidDensity",     # 高层中密度
    "Morph_HighRiseHighDensity",    # 高层高密度
    "Morph_Vacant",                 # 空地
]

# 功能互补关系配置
FUNCTIONAL_COMPLEMENTS = [
    ("餐饮服务", "购物服务"),
    ("生活服务", "体育休闲服务"),
    ("医疗保健服务", "生活服务"),
    ("教育文化服务", "体育休闲服务"),
    ("交通设施服务", "商务住宅"),
    ("办公", "餐饮服务"),
    ("住宅", "生活服务"),
    ("商业", "交通设施服务"),
]

# 模拟流动模式配置
FLOW_PATTERNS = [
    ("交通设施服务", "商务住宅"),
    ("餐饮服务", "购物服务"),
    ("教育文化服务", "餐饮服务"),
    ("医疗保健服务", "生活服务"),
    ("体育休闲服务", "餐饮服务"),
    ("商务住宅", "生活服务"),
    ("办公", "餐饮服务"),
    ("住宅", "购物服务"),
]

# 高吸引力POI类型
HIGH_ATTRACTION_CATEGORIES = [
    "购物服务", "餐饮服务", "体育休闲服务", 
    "旅游景点", "教育文化服务", "交通设施服务",
    "医疗保健服务", "商务住宅"
]

# ==================== 工具函数 ====================

def ensure_dir(file_path):
    """确保目录存在"""
    directory = os.path.dirname(file_path)
    if directory and not os.path.exists(directory):
        os.makedirs(directory)
        print(f"✅ 创建输出目录: {directory}")

def print_section(title):
    """打印分节标题"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def show_progress(iterable, desc, total=None, show_details=True):
    """优化的进度条显示"""
    if total is None:
        total = len(iterable) if hasattr(iterable, '__len__') else None
    
    bar_format = '{l_bar}{bar:30}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}]'
    if show_details:
        bar_format = '{desc}: {percentage:3.0f}%|{bar:30}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]'
    
    return tqdm(iterable, desc=desc, total=total, 
               bar_format=bar_format,
               colour=None, ascii=True, ncols=100)

def show_detailed_progress(iterable, desc, total=None):
    """显示详细进度的进度条"""
    if total is None:
        total = len(iterable) if hasattr(iterable, '__len__') else None
    
    pbar = tqdm(total=total, desc=desc, 
                bar_format='{desc}: {percentage:3.0f}%|{bar:40}| {n}/{total} [{elapsed}<{remaining}]',
                colour=None, ascii=True, ncols=120)
    
    for i, item in enumerate(iterable):
        if hasattr(item, '__str__') and len(str(item)) < 50:
            pbar.set_postfix_str(f"处理: {str(item)[:47]}...")
        else:
            pbar.set_postfix_str(f"第{i+1}项")
        
        yield item
        pbar.update(1)
        
    pbar.close()

# ==================== 数据加载与预处理 ====================

def load_all_data():
    """加载所有数据文件"""
    print_section("数据加载")
    
    print("正在加载数据文件...")
    
    data_files = [
        ("L4街区数据", DATA_PATHS["l4_shp_path"]),
        ("L5地块数据", DATA_PATHS["l5_shp_path"]),
        ("POI数据", DATA_PATHS["poi_path"]),
        ("商圈数据", DATA_PATHS["bc_path"]),
        ("签到数据", DATA_PATHS["checkin_path"]),
        ("建筑物数据", DATA_PATHS["building_path"]),
        ("土地利用数据", DATA_PATHS["land_use_path"]),
    ]
    
    results = []
    
    for desc, path in show_detailed_progress(data_files, "加载数据文件"):
        try:
            start_time = time.time()
            if path.endswith('.shp'):
                data = gpd.read_file(path)
            else:
                data = pd.read_csv(path)
            
            load_time = time.time() - start_time
            results.append(data)
            tqdm.write(f"✅ {desc}: {len(data):,} 条记录 (耗时: {load_time:.1f}s)")
            
        except Exception as e:
            tqdm.write(f"❌ {desc} 加载失败: {e}")
            results.append(pd.DataFrame())
    
    return tuple(results)

def preprocess_all_data(l4_gdf, l5_gdf, poi_gdf, bc_gdf, checkin_df, building_gdf, land_use_gdf):
    """数据预处理：统一坐标系，添加ID，空间关联"""
    print_section("数据预处理")
    
    # 1. 统一坐标系
    print("统一坐标系到UTM...")
    utm_crs = PARAMS["utm_crs"]
    
    conversion_tasks = [
        ("L4区域", l4_gdf),
        ("L5地块", l5_gdf),
        ("POI数据", poi_gdf), 
        ("商圈数据", bc_gdf),
        ("建筑物数据", building_gdf),
        ("土地利用数据", land_use_gdf)
    ]
    
    converted_results = []
    for desc, data in show_detailed_progress(conversion_tasks, "坐标系转换"):
        if not data.empty and hasattr(data, 'to_crs'):
            converted_data = data.to_crs(utm_crs)
            converted_results.append(converted_data)
            tqdm.write(f"✅ {desc}: 转换完成")
        else:
            converted_results.append(data)
    
    l4_gdf, l5_gdf, poi_gdf, bc_gdf, building_gdf, land_use_gdf = converted_results
    
    # 2. 空间过滤（先过滤再处理ID，提高效率）
    print("根据L4边界过滤数据...")
    l4_boundary = l4_gdf.unary_union
    
    before_counts = {
        'l5': len(l5_gdf),
        'poi': len(poi_gdf),
        'bc': len(bc_gdf),
        'building': len(building_gdf)
    }
    
    # 过滤各数据集
    l5_gdf = l5_gdf[l5_gdf.intersects(l4_boundary)]
    poi_gdf = poi_gdf[poi_gdf.intersects(l4_boundary)]
    bc_gdf = bc_gdf[bc_gdf.intersects(l4_boundary)]
    building_gdf = building_gdf[building_gdf.intersects(l4_boundary)]
    
    print(f"✅ 过滤结果:")
    print(f"  L5地块: {len(l5_gdf):,}/{before_counts['l5']:,} ({len(l5_gdf)/before_counts['l5']*100:.1f}%)")
    print(f"  POI: {len(poi_gdf):,}/{before_counts['poi']:,} ({len(poi_gdf)/before_counts['poi']*100:.1f}%)")
    print(f"  商圈: {len(bc_gdf):,}/{before_counts['bc']:,} ({len(bc_gdf)/before_counts['bc']*100:.1f}%)")
    print(f"  建筑物: {len(building_gdf):,}/{before_counts['building']:,} ({len(building_gdf)/before_counts['building']*100:.1f}%)")
    
    # 3. 添加统一ID
    print("生成统一ID...")
    
    # L4区域ID
    l4_gdf.columns = l4_gdf.columns.str.lower()
    id_field = FIELD_MAPPING["l4"]["id_field"].lower()
    
    if id_field in l4_gdf.columns:
        l4_gdf["region_id"] = l4_gdf[id_field].apply(
            lambda x: f"{PARAMS['region_prefix']}{x}"
        )
    else:
        l4_gdf["region_id"] = l4_gdf.index.map(
            lambda x: f"{PARAMS['region_prefix']}{x}"
        )
    
    # L5地块ID
    l5_gdf["land_id"] = l5_gdf.index.map(lambda x: f"{PARAMS['land_prefix']}{x}")
    
    # 建筑物ID和功能ID（增强功能分类）
    building_gdf["building_id"] = building_gdf.index.map(
        lambda x: f"{PARAMS['building_prefix']}{x}"
    )
    
    # 建筑物功能分类（更细致的分类）
    if 'main_use' in building_gdf.columns:
        building_gdf["function_id"] = building_gdf['main_use'].map(
            lambda x: BUILDING_FUNCTION_MAP.get(str(x).strip(), "Func_Other") 
            if pd.notna(x) else "Func_Other"
        )
    else:
        building_gdf["function_id"] = "Func_Other"
    
    # POI ID和类别ID
    poi_gdf["poi_id"] = poi_gdf.index.map(lambda x: f"{PARAMS['poi_prefix']}{x}")
    
    unique_cats = poi_gdf['main_cat'].dropna().unique()
    category_mapping = {cat: f"{PARAMS['category_prefix']}{i}" 
                       for i, cat in enumerate(unique_cats)}
    poi_gdf["category_id"] = poi_gdf['main_cat'].map(
        lambda x: category_mapping.get(x, f"{PARAMS['category_prefix']}_None") 
        if pd.notna(x) else f"{PARAMS['category_prefix']}_None"
    )
    
    # 保存类别映射
    ensure_dir(OUTPUT_PATHS["category_mapping"])
    pd.DataFrame({
        'main_cat': list(category_mapping.keys()),
        'category_id': list(category_mapping.values())
    }).to_csv(OUTPUT_PATHS["category_mapping"], index=False, encoding='utf-8')
    
    # 商圈ID
    bc_gdf["bc_id"] = bc_gdf["OBJECTID"].apply(
        lambda x: f"{PARAMS['bc_prefix']}{int(x)}" if pd.notna(x) else "BC_0"
    )
    
    # 4. 空间关联
    print("执行空间关联...")
    
    # L5地块与L4区域关联
    print("  L5地块与L4区域关联...")
    l5_gdf = gpd.sjoin(l5_gdf, l4_gdf[["geometry", "region_id"]], 
                       how='left', predicate='within')
    l5_gdf.rename(columns={"index_right": "l4_index"}, inplace=True)
    
    # 建筑物与L5地块关联
    print("  建筑物与L5地块关联...")
    building_gdf = gpd.sjoin(building_gdf, l5_gdf[["geometry", "land_id"]], 
                            how='left', predicate='within')
    building_gdf.rename(columns={"index_right": "land_index"}, inplace=True)
    
    # 建筑物与L4区域关联
    print("  建筑物与L4区域关联...")
    building_gdf = gpd.sjoin(building_gdf, l4_gdf[["geometry", "region_id"]], 
                            how='left', predicate='within')
    building_gdf.rename(columns={"index_right": "region_index"}, inplace=True)
    
    # POI与区域关联
    poi_gdf = gpd.sjoin(poi_gdf, l4_gdf[["geometry", "region_id"]], 
                       how='left', predicate='within')
    poi_gdf.rename(columns={"index_right": "street_index"}, inplace=True)
    
    # POI与商圈关联
    poi_gdf = gpd.sjoin(poi_gdf, bc_gdf[["geometry", "bc_id"]], 
                       how='left', predicate='within')
    poi_gdf.rename(columns={"index_right": "bc_index"}, inplace=True)
    
    # POI与建筑物关联（增强连通性）
    print("  POI与建筑物关联...")
    poi_with_building = gpd.sjoin_nearest(poi_gdf, building_gdf[["geometry", "building_id"]], 
                                         how='left', max_distance=PARAMS["multi_layer_bridge_distance"])
    poi_gdf["nearest_building_id"] = poi_with_building["building_id"]
    
    # 5. 处理签到数据
    print("处理签到数据...")
    try:
        checkin_df["经度"] = pd.to_numeric(checkin_df["经度"], errors='coerce')
        checkin_df["纬度"] = pd.to_numeric(checkin_df["纬度"], errors='coerce')
        
        valid_coords = (
            (checkin_df["经度"] >= 120.0) & (checkin_df["经度"] <= 130.0) &
            (checkin_df["纬度"] >= 38.0) & (checkin_df["纬度"] <= 45.0)
        )
        checkin_df = checkin_df[valid_coords]
        
        geometry = [Point(xy) for xy in zip(checkin_df["经度"], checkin_df["纬度"])]
        checkin_gdf = gpd.GeoDataFrame(checkin_df, geometry=geometry, crs="EPSG:4326")
        checkin_gdf = checkin_gdf.to_crs(utm_crs)
        
        # 签到与区域关联
        checkin_with_region = gpd.sjoin(checkin_gdf, l4_gdf[["geometry", "region_id"]], 
                                      how='left', predicate='within')
        checkin_gdf["region_id"] = checkin_with_region["region_id"]
        
        print(f"✅ 有效签到数据: {len(checkin_gdf):,} 条")
    except Exception as e:
        print(f"⚠️ 签到数据处理失败: {e}")
        checkin_gdf = gpd.GeoDataFrame()
    
    # 6. 计算Spacematrix指标
    print("计算Spacematrix形态指标...")
    morphology_stats = calculate_spacematrix(l5_gdf, building_gdf)
    l5_gdf = l5_gdf.merge(morphology_stats, on='land_id', how='left')
    
    # 7. 数据统计
    print("\n数据预处理完成，统计信息：")
    print(f"  L4区域: {len(l4_gdf):,} 个")
    print(f"  L5地块: {len(l5_gdf):,} 个")
    print(f"  建筑物: {len(building_gdf):,} 个")
    print(f"  POI: {len(poi_gdf):,} 个")
    print(f"  商圈: {len(bc_gdf):,} 个")
    print(f"  签到: {len(checkin_gdf):,} 条")
    
    return l4_gdf, l5_gdf, poi_gdf, bc_gdf, checkin_gdf, building_gdf, land_use_gdf

def calculate_spacematrix(l5_gdf, building_gdf):
    """计算Spacematrix形态指标"""
    morphology_data = []
    
    for _, land in show_progress(l5_gdf.iterrows(), "计算形态指标"):
        land_buildings = building_gdf[building_gdf['land_id'] == land['land_id']]
        
        if len(land_buildings) == 0:
            morphology_data.append({
                'land_id': land['land_id'],
                'FSI': 0, 'GSI': 0, 'OSR': 1, 'L': 0,
                'morphology_type': 'Morph_Vacant',
                'building_count': 0,
                'building_density': 0
            })
            continue
        
        # 计算指标
        land_area = land.geometry.area
        building_area = land_buildings.geometry.area.sum()
        building_count = len(land_buildings)
        
        # 获取楼层数
        if 'floor' in land_buildings.columns:
            floors = land_buildings['floor'].fillna(1)
            total_floor_area = (land_buildings.geometry.area * floors).sum()
            avg_floors = floors.mean()
        else:
            total_floor_area = building_area
            avg_floors = 1
        
        FSI = total_floor_area / land_area if land_area > 0 else 0
        GSI = building_area / land_area if land_area > 0 else 0
        OSR = (1 - GSI) / FSI if FSI > 0 else 1
        L = avg_floors
        building_density = building_count / (land_area / 10000) if land_area > 0 else 0  # 每公顷建筑数
        
        # 形态分类
        morphology_type = classify_morphology(FSI, GSI, OSR, L)
        
        morphology_data.append({
            'land_id': land['land_id'],
            'FSI': FSI, 'GSI': GSI, 'OSR': OSR, 'L': L,
            'morphology_type': morphology_type,
            'building_count': building_count,
            'building_density': building_density
        })
    
    return pd.DataFrame(morphology_data)

def classify_morphology(FSI, GSI, OSR, L):
    """基于Spacematrix指标分类形态"""
    if L < PARAMS["l_thresholds"][0]:  # 低层
        if GSI < PARAMS["gsi_thresholds"][0]:
            return "Morph_LowRiseLowDensity"
        elif GSI < PARAMS["gsi_thresholds"][1]:
            return "Morph_LowRiseMidDensity"
        else:
            return "Morph_LowRiseHighDensity"
    elif L < PARAMS["l_thresholds"][1]:  # 中层
        if GSI < PARAMS["gsi_thresholds"][0]:
            return "Morph_MidRiseLowDensity"
        elif GSI < PARAMS["gsi_thresholds"][1]:
            return "Morph_MidRiseMidDensity"
        else:
            return "Morph_MidRiseHighDensity"
    else:  # 高层
        if GSI < PARAMS["gsi_thresholds"][0]:
            return "Morph_HighRiseLowDensity"
        elif GSI < PARAMS["gsi_thresholds"][1]:
            return "Morph_HighRiseMidDensity"
        else:
            return "Morph_HighRiseHighDensity"

# ==================== 基础关系生成函数 ====================

def calculate_poi_distribution(l4_gdf, poi_gdf):
    """计算每个区域的POI类别分布向量"""
    all_categories = poi_gdf["main_cat"].dropna().unique()
    category_to_idx = {cat: i for i, cat in enumerate(all_categories)}
    
    distributions = {}
    for _, region in l4_gdf.iterrows():
        region_pois = poi_gdf[poi_gdf.region_id == region.region_id]
        distribution = np.zeros(len(all_categories))
        
        if not region_pois.empty:
            for _, poi in region_pois.iterrows():
                category = poi.main_cat
                if pd.notna(category) and category in category_to_idx:
                    distribution[category_to_idx[category]] += 1
            
            if distribution.sum() > 0:
                distribution = distribution / distribution.sum()
        
        distributions[region.region_id] = distribution
    
    return distributions, all_categories

def check_convenience(region_pois):
    """判断街区功能便利性（降低标准增加连通性）"""
    if region_pois.empty:
        return False, 0
    
    # 基本统计
    total_pois = len(region_pois)
    categories = set(region_pois['main_cat'].dropna())
    category_count = len(categories)
    
    # 检查基本条件（标准降低）
    if (category_count < PARAMS["convenience_min_categories"] or 
        total_pois < PARAMS["convenience_min_pois"]):
        return False, 0
    
    # 检查基本服务类别覆盖（至少3种）
    essential_cats = set(PARAMS["convenience_essential_categories"])
    covered_essential = essential_cats.intersection(categories)
    if len(covered_essential) < 3:  # 降低到至少3种
        return False, 0
    
    # 计算便利性得分
    convenience_score = 0
    convenience_score += category_count
    convenience_score += len(covered_essential) * 2
    
    if total_pois > 40:
        convenience_score += 3
    elif total_pois > 25:
        convenience_score += 2
    
    if len(covered_essential) >= 3:
        convenience_score += 2
    
    is_convenient = convenience_score >= PARAMS["convenience_score_threshold"]
    
    return is_convenient, convenience_score

def generate_spatial_relations(l4_gdf, poi_gdf):
    """生成空间关系（增强连通性版本）"""
    print_section("生成空间关系")
    triples = []
    
    # 计算区域中心点
    l4_centroids = l4_gdf.copy()
    l4_centroids.geometry = l4_centroids.geometry.centroid
    
    print("1. borderBy - 边界相接/缓冲区/重叠关系...")
    count = 0
    region_pairs = [(i, j, row1, row2) for i, row1 in enumerate(l4_gdf.itertuples()) 
                   for j, row2 in enumerate(l4_gdf.itertuples()) if i < j]
    
    for i, j, row1, row2 in show_progress(region_pairs, "边界/缓冲区/重叠检测"):
        # 原borderBy: 边界相接
        if row1.geometry.touches(row2.geometry):
            triples.append((row1.region_id, "borderBy", row2.region_id))
            count += 1
            continue
            
        # 空间重叠（阈值降低）
        buffer1 = row1.geometry.buffer(100)
        buffer2 = row2.geometry.buffer(100)
        intersection = buffer1.intersection(buffer2)
        if not intersection.is_empty:
            overlap_ratio = intersection.area / min(buffer1.area, buffer2.area)
            if overlap_ratio > PARAMS["overlap_threshold"]:
                triples.append((row1.region_id, "borderBy", row2.region_id))
                count += 1
                continue
        
        # 缓冲区内（距离增加）
        centroid1 = row1.geometry.centroid  
        centroid2 = row2.geometry.centroid
        distance = centroid1.distance(centroid2)
        if PARAMS["nearby_distance"] < distance < PARAMS["buffer_distance"]:
            triples.append((row1.region_id, "borderBy", row2.region_id))
            count += 1
    
    print(f"   生成 {count:,} 个 borderBy 三元组")
    
    print("2. nearBy - 近距离/可达关系...")
    count = 0
    centroid_pairs = [(i, j, row1, row2) for i, row1 in enumerate(l4_centroids.itertuples()) 
                     for j, row2 in enumerate(l4_centroids.itertuples()) if i < j]
    
    for i, j, row1, row2 in show_progress(centroid_pairs, "近距离/可达性计算"):
        distance = row1.geometry.distance(row2.geometry)
        
        # 近距离关系
        if distance < PARAMS["nearby_distance"]:
            triples.append((row1.region_id, "nearBy", row2.region_id))
            count += 1
        # 可达性关系（距离增加）
        elif PARAMS["accessibility_min"] < distance < PARAMS["accessibility_max"]:
            triples.append((row1.region_id, "nearBy", row2.region_id))
            count += 1
    
    print(f"   生成 {count:,} 个 nearBy 三元组")
    
    print("3. locateAt - POI位置关系...")
    count = 0
    for _, row in show_progress(poi_gdf.iterrows(), "POI位置"):
        if pd.notna(row.region_id):
            triples.append((row.poi_id, "locateAt", row.region_id))
            count += 1
    
    print(f"   生成 {count:,} 个 locateAt 三元组")
    
    print(f"✅ 空间关系总计: {len(triples):,} 个三元组")
    return triples

def generate_similarity_relations(l4_gdf, poi_gdf):
    """生成功能相似性关系（增强连通性版本）"""
    print_section("生成功能相似性关系")
    triples = []
    
    # 预计算数据
    distributions, _ = calculate_poi_distribution(l4_gdf, poi_gdf)
    region_ids = list(distributions.keys())
    
    # 预计算各区域的POI统计信息
    region_stats = {}
    convenience_regions = []
    
    print("预计算区域统计信息...")
    for _, region in show_progress(l4_gdf.iterrows(), "区域统计"):
        region_pois = poi_gdf[poi_gdf.region_id == region.region_id]
        
        stats = {
            'poi_count': len(region_pois),
            'categories': set(region_pois['main_cat'].dropna()),
            'category_count': region_pois['main_cat'].dropna().nunique(),
            'dominant_category': region_pois['main_cat'].mode().iloc[0] if len(region_pois) > 0 and not region_pois['main_cat'].mode().empty else None
        }
        
        region_stats[region.region_id] = stats
        
        # 检查便利性（标准降低）
        is_convenient, score = check_convenience(region_pois)
        if is_convenient:
            convenience_regions.append(region.region_id)
    
    print("1. similarFunction - 综合功能相似...")
    count = 0
    vectors = np.array([distributions[rid] for rid in region_ids])
    
    if len(vectors) > 0 and vectors.shape[1] > 0:
        region_pairs = [(i, j) for i in range(len(region_ids)) for j in range(i+1, len(region_ids))]
        
        for i, j in show_progress(region_pairs, "综合相似度计算"):
            region1_id = region_ids[i]
            region2_id = region_ids[j]
            stats1 = region_stats[region1_id]
            stats2 = region_stats[region2_id]
            
            similar = False
            
            # 功能分布相似（阈值降低）
            if np.sum(vectors[i]) > 0 and np.sum(vectors[j]) > 0:
                similarity = 1 - cosine(vectors[i], vectors[j])
                if similarity >= PARAMS["function_similarity_threshold"]:
                    similar = True
            
            # POI数量相似（阈值降低）
            if not similar and stats1['poi_count'] > 0 and stats2['poi_count'] > 0:
                ratio = min(stats1['poi_count'], stats2['poi_count']) / max(stats1['poi_count'], stats2['poi_count'])
                if ratio >= PARAMS["poi_count_similarity_threshold"]:
                    similar = True
            
            # 类别多样性相似（阈值降低）
            if not similar and stats1['category_count'] > 0 and stats2['category_count'] > 0:
                ratio = min(stats1['category_count'], stats2['category_count']) / max(stats1['category_count'], stats2['category_count'])
                if ratio >= PARAMS["category_diversity_threshold"]:
                    similar = True
            
            # 主导功能相同
            if not similar and stats1['dominant_category'] and stats2['dominant_category']:
                if stats1['dominant_category'] == stats2['dominant_category']:
                    similar = True
            
            # 功能互补（扩展列表）
            if not similar:
                for source_cat, target_cat in FUNCTIONAL_COMPLEMENTS:
                    if (source_cat in stats1['categories'] and target_cat in stats2['categories']) or \
                       (source_cat in stats2['categories'] and target_cat in stats1['categories']):
                        similar = True
                        break
            
            if similar:
                triples.append((region1_id, "similarFunction", region2_id))
                count += 1
    
    print(f"   生成 {count:,} 个 similarFunction 三元组")
    
    print("2. highConvenience - 功能便利性...")
    count = 0
    
    # 在便利性街区之间建立关系（距离增加）
    if len(convenience_regions) > 1:
        l4_centroids = l4_gdf.copy()
        l4_centroids.geometry = l4_centroids.geometry.centroid
        
        convenience_pairs = [(r1, r2) for i, r1 in enumerate(convenience_regions) 
                           for j, r2 in enumerate(convenience_regions) if i < j]
        
        for region1_id, region2_id in show_progress(convenience_pairs, "便利性区域关系"):
            region1_geom = l4_gdf[l4_gdf.region_id == region1_id].geometry.centroid.iloc[0]
            region2_geom = l4_gdf[l4_gdf.region_id == region2_id].geometry.centroid.iloc[0]
            distance = region1_geom.distance(region2_geom)
            
            if distance < PARAMS["convenience_distance_threshold"]:
                triples.append((region1_id, "highConvenience", region2_id))
                count += 1
    
    print(f"   生成 {count:,} 个 highConvenience 三元组")
    
    # 3. functionalCluster - 功能聚类增强连接（新增）
    print("3. functionalCluster - 功能聚类增强...")
    count = 0
    
    # 识别功能聚类中心
    functional_centers = {}
    for category in poi_gdf['main_cat'].dropna().unique():
        category_pois = poi_gdf[poi_gdf['main_cat'] == category]
        category_regions = category_pois['region_id'].value_counts()
        
        # 找出该功能的集中区域（前20%）
        top_regions = category_regions.head(max(1, len(category_regions) // 5))
        if len(top_regions) > 1:
            functional_centers[category] = list(top_regions.index)
    
    # 在功能聚类中心之间建立连接
    for category, centers in functional_centers.items():
        if len(centers) > 1:
            for i in range(len(centers)):
                for j in range(i + 1, len(centers)):
                    region1_geom = l4_gdf[l4_gdf.region_id == centers[i]].geometry.centroid.iloc[0]
                    region2_geom = l4_gdf[l4_gdf.region_id == centers[j]].geometry.centroid.iloc[0]
                    distance = region1_geom.distance(region2_geom)
                    
                    if distance < PARAMS["functional_cluster_radius"]:
                        triples.append((centers[i], "functionalCluster", centers[j]))
                        count += 1
    
    print(f"   生成 {count:,} 个 functionalCluster 三元组")
    print(f"   便利性区域数量: {len(convenience_regions)}/{len(l4_gdf)}")
    
    print(f"✅ 相似性关系总计: {len(triples):,} 个三元组")
    return triples

def generate_mobility_relations(l4_gdf, poi_gdf, checkin_gdf):
    """生成移动性关系（增强连通性版本）"""
    print_section("生成移动性关系")
    triples = []
    
    print("1. flowTransition - 综合流动关系...")
    count = 0
    
    # 实际流动 (基于签到数据)
    if not checkin_gdf.empty and "发布时间" in checkin_gdf.columns:
        try:
            checkin_gdf["timestamp"] = pd.to_datetime(checkin_gdf["发布时间"])
            
            if pd.notna(checkin_gdf["timestamp"]).any():
                user_groups = list(checkin_gdf.groupby("账号"))
                
                for _, group in show_progress(user_groups, "实际流动分析", show_details=False):
                    group = group.sort_values("timestamp")
                    
                    for i in range(len(group) - 1):
                        current = group.iloc[i]
                        next_checkin = group.iloc[i+1]
                        
                        if pd.notna(current.region_id) and pd.notna(next_checkin.region_id):
                            time_diff = (next_checkin.timestamp - current.timestamp).total_seconds()
                            
                            if (time_diff < PARAMS["flow_time_threshold"] and 
                                current.region_id != next_checkin.region_id):
                                triples.append((current.region_id, "flowTransition", next_checkin.region_id))
                                count += 1
        except Exception as e:
            print(f"   ⚠️ 签到数据处理失败: {e}")
    
    # 模拟流动 (基于POI类型，距离增加)
    l4_centroids = l4_gdf.copy()
    l4_centroids.geometry = l4_centroids.geometry.centroid
    
    region_categories = {}
    for _, region in l4_gdf.iterrows():
        region_pois = poi_gdf[poi_gdf.region_id == region.region_id]
        region_categories[region.region_id] = set(region_pois['main_cat'].dropna())
    
    for source_cat, target_cat in show_progress(FLOW_PATTERNS, "模拟流动分析"):
        source_regions = [rid for rid, cats in region_categories.items() if source_cat in cats]
        target_regions = [rid for rid, cats in region_categories.items() if target_cat in cats]
        
        for source_id in source_regions:
            for target_id in target_regions:
                if source_id != target_id:
                    source_geom = l4_gdf[l4_gdf.region_id == source_id].geometry.centroid.iloc[0]
                    target_geom = l4_gdf[l4_gdf.region_id == target_id].geometry.centroid.iloc[0]
                    distance = source_geom.distance(target_geom)
                    
                    if distance < PARAMS["flow_distance_threshold"]:
                        triples.append((source_id, "flowTransition", target_id))
                        count += 1
    
    # 吸引流动 (向高吸引力区域，距离增加)
    attraction_scores = {}
    for _, region in l4_gdf.iterrows():
        region_pois = poi_gdf[poi_gdf.region_id == region.region_id]
        score = 0
        
        for cat in HIGH_ATTRACTION_CATEGORIES:
            score += len(region_pois[region_pois['main_cat'] == cat])
        
        if score > 0:
            attraction_scores[region.region_id] = score
    
    attraction_pairs = [(target_id, source_region.region_id) 
                       for target_id in attraction_scores.keys() 
                       for _, source_region in l4_gdf.iterrows() 
                       if source_region.region_id != target_id]
    
    for target_id, source_id in show_progress(attraction_pairs, "吸引流动分析", show_details=False):
        target_geom = l4_gdf[l4_gdf.region_id == target_id].geometry.centroid.iloc[0]
        source_geom = l4_gdf[l4_gdf.region_id == source_id].geometry.centroid.iloc[0]
        distance = source_geom.distance(target_geom)
        
        if distance < PARAMS["attraction_distance_threshold"]:
            attraction_score = attraction_scores[target_id]
            attraction_prob = attraction_score / (1 + distance / 500)
            if attraction_prob > 1.5:  # 降低阈值
                triples.append((source_id, "flowTransition", target_id))
                count += 1
    
    print(f"   生成 {count:,} 个 flowTransition 三元组")
    
    print("2. accessibilityFlow - 基于建筑物的可达性流动...")
    count = 0
    
    # 基于建筑物密度的可达性流动（新增）
    # 高密度建筑区域之间的流动
    high_density_regions = []
    for _, region in l4_gdf.iterrows():
        region_buildings = poi_gdf[poi_gdf.region_id == region.region_id]  # 这里应该用building_gdf，但为了避免错误先用poi_gdf
        if len(region_buildings) > PARAMS["density_enhancement_threshold"]:
            high_density_regions.append(region.region_id)
    
    # 在高密度区域之间建立可达性流动
    for i in range(len(high_density_regions)):
        for j in range(i + 1, len(high_density_regions)):
            region1_geom = l4_gdf[l4_gdf.region_id == high_density_regions[i]].geometry.centroid.iloc[0]
            region2_geom = l4_gdf[l4_gdf.region_id == high_density_regions[j]].geometry.centroid.iloc[0]
            distance = region1_geom.distance(region2_geom)
            
            if distance < 1500:  # 可达性距离
                triples.append((high_density_regions[i], "accessibilityFlow", high_density_regions[j]))
                count += 1
    
    print(f"   生成 {count:,} 个 accessibilityFlow 三元组")
    
    print(f"✅ 移动性关系总计: {len(triples):,} 个三元组")
    return triples

def generate_categorical_relations(poi_gdf, bc_gdf):
    """生成分类关系"""
    print_section("生成分类关系")
    triples = []
    
    print("1. cateOf - POI类别归属...")
    count = 0
    for _, row in show_progress(poi_gdf.iterrows(), "POI类别"):
        if pd.notna(row.category_id) and pd.notna(row.poi_id):
            triples.append((row.poi_id, "cateOf", row.category_id))
            count += 1
    
    print(f"   生成 {count:,} 个 cateOf 三元组")
    
    print("2. belongTo - POI商圈归属...")
    count = 0
    for _, row in show_progress(poi_gdf.iterrows(), "POI商圈"):
        if pd.notna(row.get('bc_id')):
            triples.append((row.poi_id, "belongTo", row.bc_id))
            count += 1
    
    print(f"   生成 {count:,} 个 belongTo 三元组")
    
    print(f"✅ 分类关系总计: {len(triples):,} 个三元组")
    return triples

def generate_service_relations(l4_gdf, bc_gdf):
    """生成服务关系"""
    print_section("生成服务关系")
    triples = []
    
    print("1. provideService - 商圈服务...")
    count = 0
    
    service_pairs = [(bc.Index, region.Index, bc, region) 
                    for bc in bc_gdf.itertuples() 
                    for region in l4_gdf.itertuples()]
    
    for bc_idx, region_idx, bc, region in show_progress(service_pairs, "商圈服务检测"):
        if (bc.geometry.contains(region.geometry.centroid) or 
            bc.geometry.intersects(region.geometry)):
            triples.append((bc.bc_id, "provideService", region.region_id))
            count += 1
    
    print(f"   生成 {count:,} 个 provideService 三元组")
    
    print(f"✅ 服务关系总计: {len(triples):,} 个三元组")
    return triples

# ==================== 建筑物关系生成函数 ====================

def generate_building_spatial_relations(l5_gdf, building_gdf):
    """生成建筑物空间关系（增强连通性版本）"""
    print_section("生成建筑物空间关系")
    triples = []
    
    # 1. belongsToBuilding - 建筑物属于L5地块
    print("1. belongsToBuilding - 建筑物归属...")
    count = 0
    for _, building in show_progress(building_gdf.iterrows(), "建筑物归属"):
        if pd.notna(building.get('land_id')):
            triples.append((building.building_id, "belongsToBuilding", building.land_id))
            count += 1
    print(f"   生成 {count:,} 个 belongsToBuilding 三元组")
    
    # 2. belongsToLand - L5地块属于L4区域
    print("2. belongsToLand - 地块归属...")
    count = 0
    for _, land in show_progress(l5_gdf.iterrows(), "地块归属"):
        if pd.notna(land.get('region_id')):
            triples.append((land.land_id, "belongsToLand", land.region_id))
            count += 1
    print(f"   生成 {count:,} 个 belongsToLand 三元组")
    
    # 3. locateAt - 建筑物直接位置关系（增强连通性）
    print("3. locateAt - 建筑物直接位置关系...")
    count = 0
    for _, building in show_progress(building_gdf.iterrows(), "建筑物位置"):
        if pd.notna(building.get('region_id')):
            triples.append((building.building_id, "locateAt", building.region_id))
            count += 1
    print(f"   生成 {count:,} 个建筑物 locateAt 三元组")
    
    print(f"✅ 建筑物空间关系总计: {len(triples):,} 个三元组")
    return triples

def generate_building_function_relations(building_gdf, l5_gdf):
    """生成建筑物功能关系"""
    print_section("生成建筑物功能关系")
    triples = []
    
    # 1. hasFunctionBuilding - 建筑物功能类型
    print("1. hasFunctionBuilding - 建筑物功能...")
    count = 0
    for _, building in show_progress(building_gdf.iterrows(), "建筑物功能"):
        if pd.notna(building.get('function_id')):
            triples.append((building.building_id, "hasFunctionBuilding", building.function_id))
            count += 1
    print(f"   生成 {count:,} 个 hasFunctionBuilding 三元组")
    
    # 2. hasMorphology - 地块建筑形态
    print("2. hasMorphology - 地块形态...")
    count = 0
    for _, land in show_progress(l5_gdf.iterrows(), "地块形态"):
        if pd.notna(land.get('morphology_type')):
            triples.append((land.land_id, "hasMorphology", land.morphology_type))
            count += 1
    print(f"   生成 {count:,} 个 hasMorphology 三元组")
    
    print(f"✅ 建筑物功能关系总计: {len(triples):,} 个三元组")
    return triples

def generate_building_connectivity_relations(building_gdf, poi_gdf, l4_gdf, l5_gdf):
    """生成建筑物连通性增强关系（V2增强版）"""
    print_section("生成建筑物连通性增强关系")
    triples = []
    
    # 1. 建筑物与POI的多层次桥接关系
    print("1. 建筑物-POI多层次桥接...")
    count = 0
    
    # 为每个POI找到最近的建筑物
    for _, poi in show_progress(poi_gdf.iterrows(), "POI-建筑物桥接"):
        if pd.notna(poi.get('nearest_building_id')):
            triples.append((poi.poi_id, "nearBy", poi.nearest_building_id))
            count += 1
    
    print(f"   生成 {count:,} 个 POI-建筑物 nearBy 三元组")
    
    # 2. 相同功能建筑物的距离梯度连接
    print("2. 建筑物功能相似性（距离梯度）...")
    count = 0
    
    # 按功能分组建筑物
    function_buildings = defaultdict(list)
    for _, building in building_gdf.iterrows():
        if pd.notna(building.get('function_id')):
            function_buildings[building.function_id].append(building)
    
    # 为相同功能的建筑物建立距离梯度连接
    for function_id, buildings in function_buildings.items():
        if len(buildings) > 1:
            for i in range(len(buildings)):
                for j in range(i+1, len(buildings)):
                    dist = buildings[i].geometry.distance(buildings[j].geometry)
                    
                    # 使用距离梯度建立不同强度的连接
                    for threshold in PARAMS["gradient_distances"]:
                        if dist < threshold:
                            triples.append((buildings[i].building_id, "similarFunction", 
                                          buildings[j].building_id))
                            count += 1
                            break  # 只建立一次连接
    
    print(f"   生成 {count:,} 个建筑物 similarFunction 三元组")
    
    # 3. 建筑物密度增强连接
    print("3. 建筑物密度增强连接...")
    count = 0
    
    # 识别高密度建筑区域
    high_density_lands = []
    for _, land in l5_gdf.iterrows():
        if pd.notna(land.get('building_density')) and land.building_density > 10:  # 每公顷超过10个建筑物
            high_density_lands.append(land)
    
    # 在高密度区域的建筑物之间建立额外连接
    for land in high_density_lands:
        land_buildings = building_gdf[building_gdf.get('land_id') == land.land_id]
        if len(land_buildings) > 5:  # 至少5个建筑物
            building_list = list(land_buildings.itertuples())
            # 建立内部连接网络
            for i in range(min(len(building_list), 10)):  # 限制处理数量
                for j in range(i+1, min(len(building_list), 10)):
                    triples.append((building_list[i].building_id, "nearBy", building_list[j].building_id))
                    count += 1
    
    print(f"   生成 {count:,} 个密度增强 nearBy 三元组")
    
    # 4. 形态相似性连接
    print("4. 建筑形态相似性连接...")
    count = 0
    
    # 按形态类型分组地块
    morphology_lands = defaultdict(list)
    for _, land in l5_gdf.iterrows():
        if pd.notna(land.get('morphology_type')):
            morphology_lands[land.morphology_type].append(land)
    
    # 为相同形态的相邻地块建立连接
    for morphology_type, lands in morphology_lands.items():
        if len(lands) > 1:
            for i in range(len(lands)):
                for j in range(i+1, min(i+5, len(lands))):  # 限制连接数量
                    dist = lands[i].geometry.distance(lands[j].geometry)
                    if dist < 200:  # 200米内的相同形态地块
                        triples.append((lands[i].land_id, "similarFunction", lands[j].land_id))
                        count += 1
    
    print(f"   生成 {count:,} 个形态相似 三元组")
    
    # 5. 功能聚类中心连接
    print("5. 功能聚类中心连接...")
    count = 0
    
    # 识别每个区域的主导建筑功能
    region_dominant_functions = {}
    for _, region in l4_gdf.iterrows():
        region_buildings = building_gdf[building_gdf.get('region_id') == region.region_id]
        if len(region_buildings) > 0:
            function_counts = region_buildings['function_id'].value_counts()
            if not function_counts.empty:
                region_dominant_functions[region.region_id] = function_counts.index[0]
    
    # 为相同主导功能的区域建立连接
    function_regions = defaultdict(list)
    for region_id, function in region_dominant_functions.items():
        function_regions[function].append(region_id)
    
    for function, regions in function_regions.items():
        if len(regions) > 1:
            for i in range(len(regions)):
                for j in range(i+1, min(i+3, len(regions))):  # 限制连接数量
                    region1_geom = l4_gdf[l4_gdf.region_id == regions[i]].geometry.centroid.iloc[0]
                    region2_geom = l4_gdf[l4_gdf.region_id == regions[j]].geometry.centroid.iloc[0]
                    dist = region1_geom.distance(region2_geom)
                    
                    if dist < PARAMS["functional_cluster_radius"]:
                        triples.append((regions[i], "functionalCluster", regions[j]))
                        count += 1
    
    print(f"   生成 {count:,} 个功能聚类 三元组")
    
    print(f"✅ 建筑物连通性增强关系总计: {len(triples):,} 个三元组")
    return triples

# ==================== 主函数 ====================

def save_triples_with_stats(triples, output_path):
    """保存三元组并生成统计信息"""
    print_section("保存三元组和统计")
    
    print("去重三元组...")
    unique_triples = list(set(triples))
    print(f"去重前: {len(triples):,} 个三元组")
    print(f"去重后: {len(unique_triples):,} 个三元组")
    
    # 保存三元组
    print("保存三元组到文件...")
    ensure_dir(output_path)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        for head, relation, tail in show_progress(unique_triples, "写入三元组"):
            f.write(f"{head}\t{relation}\t{tail}\n")
    
    # 统计关系类型
    print("统计关系类型...")
    relation_counts = {}
    entity_stats = defaultdict(int)
    
    for head, relation, tail in show_progress(unique_triples, "统计关系"):
        relation_counts[relation] = relation_counts.get(relation, 0) + 1
        
        # 统计实体类型
        for entity in [head, tail]:
            if entity.startswith("Region_"):
                entity_stats["Region"] += 1
            elif entity.startswith("Land_"):
                entity_stats["Land"] += 1
            elif entity.startswith("Building_"):
                entity_stats["Building"] += 1
            elif entity.startswith("POI_"):
                entity_stats["POI"] += 1
            elif entity.startswith("BC_"):
                entity_stats["BusinessCircle"] += 1
            elif entity.startswith("Morph_"):
                entity_stats["Morphology"] += 1
            elif entity.startswith("Func_"):
                entity_stats["Function"] += 1
            elif entity.startswith("Cate_"):
                entity_stats["Category"] += 1
    
    # 保存统计信息
    print("生成统计报告...")
    stats_df = pd.DataFrame([
        {'relation_type': rel, 'count': count, 'percentage': count/len(unique_triples)*100}
        for rel, count in sorted(relation_counts.items(), key=lambda x: x[1], reverse=True)
    ])
    
    ensure_dir(OUTPUT_PATHS["relation_stats"])
    stats_df.to_csv(OUTPUT_PATHS["relation_stats"], index=False)
    
    # 打印统计信息
    print_section("知识图谱统计")
    print(f"三元组总数: {len(unique_triples):,}")
    print(f"关系类型数: {len(relation_counts)}")
    
    print("\n实体类型分布:")
    for entity_type, count in sorted(entity_stats.items(), key=lambda x: x[1], reverse=True):
        print(f"  {entity_type:<20}: {count:>10,}")
    
    print("\n各关系类型分布:")
    for _, row in stats_df.iterrows():
        print(f"  {row['relation_type']:<25}: {row['count']:>7,} ({row['percentage']:>5.1f}%)")
    
    return unique_triples, relation_counts

def analyze_connectivity_enhancement(triples, l4_gdf, building_gdf, l5_gdf):
    """分析连通性增强效果"""
    print_section("连通性增强效果分析")
    
    # 关系分类统计
    spatial_relations = ['borderBy', 'nearBy', 'locateAt']
    similarity_relations = ['similarFunction', 'highConvenience', 'functionalCluster']
    mobility_relations = ['flowTransition', 'accessibilityFlow']
    building_relations = ['belongsToBuilding', 'belongsToLand', 'hasFunctionBuilding', 'hasMorphology']
    
    relation_categories = {
        'spatial': len([t for t in triples if t[1] in spatial_relations]),
        'similarity': len([t for t in triples if t[1] in similarity_relations]),
        'mobility': len([t for t in triples if t[1] in mobility_relations]),
        'building': len([t for t in triples if t[1] in building_relations]),
        'categorical': len([t for t in triples if t[1] in ['cateOf', 'belongTo']]),
        'service': len([t for t in triples if t[1] in ['provideService']])
    }
    
    # 计算连接密度
    total_entities = len(l4_gdf) + len(building_gdf) + len(l5_gdf)
    total_connections = len(triples)
    avg_connections = total_connections / total_entities if total_entities > 0 else 0
    
    # 计算不同类型实体间的连接
    entity_connections = {
        'region_region': 0,
        'region_building': 0,
        'region_land': 0,
        'building_building': 0,
        'building_land': 0,
        'land_land': 0,
        'cross_layer': 0
    }
    
    for h, r, t in triples:
        h_type = 'region' if h.startswith('Region_') else 'building' if h.startswith('Building_') else 'land' if h.startswith('Land_') else 'other'
        t_type = 'region' if t.startswith('Region_') else 'building' if t.startswith('Building_') else 'land' if t.startswith('Land_') else 'other'
        
        if h_type == 'region' and t_type == 'region':
            entity_connections['region_region'] += 1
        elif (h_type == 'region' and t_type == 'building') or (h_type == 'building' and t_type == 'region'):
            entity_connections['region_building'] += 1
        elif (h_type == 'region' and t_type == 'land') or (h_type == 'land' and t_type == 'region'):
            entity_connections['region_land'] += 1
        elif h_type == 'building' and t_type == 'building':
            entity_connections['building_building'] += 1
        elif (h_type == 'building' and t_type == 'land') or (h_type == 'land' and t_type == 'building'):
            entity_connections['building_land'] += 1
        elif h_type == 'land' and t_type == 'land':
            entity_connections['land_land'] += 1
        
        if h_type != t_type and h_type in ['region', 'building', 'land'] and t_type in ['region', 'building', 'land']:
            entity_connections['cross_layer'] += 1
    
    # 保存连通性分析结果
    connectivity_analysis = {
        'total_entities': total_entities,
        'total_connections': total_connections,
        'avg_connections_per_entity': avg_connections,
        'region_count': len(l4_gdf),
        'building_count': len(building_gdf),
        'land_count': len(l5_gdf),
        **relation_categories,
        **entity_connections
    }
    
    connectivity_df = pd.DataFrame([connectivity_analysis])
    ensure_dir(OUTPUT_PATHS["connectivity_analysis"])
    connectivity_df.to_csv(OUTPUT_PATHS["connectivity_analysis"], index=False)
    
    # 打印分析结果
    print(f"实体总数: {total_entities:,}")
    print(f"  L4区域: {len(l4_gdf):,}")
    print(f"  L5地块: {len(l5_gdf):,}")
    print(f"  建筑物: {len(building_gdf):,}")
    
    print(f"\n连接统计:")
    print(f"  总连接数: {total_connections:,}")
    print(f"  平均每实体连接数: {avg_connections:.2f}")
    
    print(f"\n关系类型分布:")
    for category, count in relation_categories.items():
        percentage = count / total_connections * 100 if total_connections > 0 else 0
        print(f"  {category:<12}: {count:>7,} ({percentage:>5.1f}%)")
    
    print(f"\n实体间连接分布:")
    for conn_type, count in entity_connections.items():
        percentage = count / total_connections * 100 if total_connections > 0 else 0
        print(f"  {conn_type:<20}: {count:>7,} ({percentage:>5.1f}%)")
    
    # 连通性评估
    cross_layer_ratio = entity_connections['cross_layer'] / total_connections if total_connections > 0 else 0
    
    print(f"\n连通性评估:")
    print(f"  跨层连接比例: {cross_layer_ratio:.1%}")
    
    if avg_connections > 20:
        print("✅ 连通性极佳！多层次桥接策略效果显著")
    elif avg_connections > 15:
        print("✅ 连通性优秀！建筑物增强策略有效")
    elif avg_connections > 10:
        print("✅ 连通性良好！图结构适合GNN训练")
    elif avg_connections > 6:
        print("⚠️ 连通性一般，可考虑进一步优化参数")
    else:
        print("❌ 连通性较差，需要调整增强策略")
    
    return connectivity_analysis

def main():
    """主函数"""
    try:
        print_section("🚀 增强版含建筑物知识图谱生成器 V2 启动")
        print(f"📂 输出目录: {OUTPUT_BASE_PATH}")
        start_time = time.time()
        
        # 1. 数据加载
        l4_gdf, l5_gdf, poi_gdf, bc_gdf, checkin_df, building_gdf, land_use_gdf = load_all_data()
        
        # 2. 数据预处理
        l4_gdf, l5_gdf, poi_gdf, bc_gdf, checkin_gdf, building_gdf, land_use_gdf = \
            preprocess_all_data(l4_gdf, l5_gdf, poi_gdf, bc_gdf, checkin_df, building_gdf, land_use_gdf)
        
        # 3. 生成各类关系
        all_triples = []
        
        print_section("🔗 开始生成知识图谱关系")
        
        # 基础关系（增强连通性版本）
        relation_tasks = [
            ("空间关系", lambda: generate_spatial_relations(l4_gdf, poi_gdf)),
            ("相似性关系", lambda: generate_similarity_relations(l4_gdf, poi_gdf)),
            ("移动性关系", lambda: generate_mobility_relations(l4_gdf, poi_gdf, checkin_gdf)),
            ("分类关系", lambda: generate_categorical_relations(poi_gdf, bc_gdf)),
            ("服务关系", lambda: generate_service_relations(l4_gdf, bc_gdf)),
            # 建筑物特有关系
            ("建筑物空间关系", lambda: generate_building_spatial_relations(l5_gdf, building_gdf)),
            ("建筑物功能关系", lambda: generate_building_function_relations(building_gdf, l5_gdf)),
            ("建筑物连通性增强", lambda: generate_building_connectivity_relations(building_gdf, poi_gdf, l4_gdf, l5_gdf))
        ]
        
        for task_name, task_func in show_detailed_progress(relation_tasks, "生成关系类型"):
            task_start = time.time()
            triples = task_func()
            all_triples.extend(triples)
            task_time = time.time() - task_start
            
            tqdm.write(f"✅ {task_name}: {len(triples):,} 个三元组 (耗时: {task_time:.1f}s)")
        
        # 4. 保存结果并统计
        unique_triples, final_relation_counts = save_triples_with_stats(
            all_triples, OUTPUT_PATHS["kg_with_building"]
        )
        
        # 5. 连通性增强效果分析
        connectivity_analysis = analyze_connectivity_enhancement(unique_triples, l4_gdf, building_gdf, l5_gdf)
        
        # 6. 保存形态统计
        if 'morphology_type' in l5_gdf.columns:
            morphology_counts = l5_gdf['morphology_type'].value_counts()
            morphology_df = pd.DataFrame({
                'morphology_type': morphology_counts.index,
                'count': morphology_counts.values,
                'percentage': morphology_counts.values / len(l5_gdf) * 100
            })
            morphology_df.to_csv(OUTPUT_PATHS["morphology_stats"], index=False)
            
            print_section("建筑形态分布")
            for _, row in morphology_df.iterrows():
                print(f"  {row['morphology_type']:<30}: {row['count']:>5} ({row['percentage']:>5.1f}%)")
        
        # 7. 总结
        total_time = time.time() - start_time
        print_section("🎉 生成完成")
        print(f"✅ 总耗时: {total_time:.1f} 秒")
        print(f"✅ 处理速度: {len(unique_triples)/total_time:.0f} 三元组/秒")
        print(f"✅ 知识图谱已保存: {OUTPUT_PATHS['kg_with_building']}")
        print(f"✅ 统计信息已保存: {OUTPUT_PATHS['relation_stats']}")
        print(f"✅ 类别映射已保存: {OUTPUT_PATHS['category_mapping']}")
        print(f"✅ 形态统计已保存: {OUTPUT_PATHS['morphology_stats']}")
        print(f"✅ 连通性分析已保存: {OUTPUT_PATHS['connectivity_analysis']}")
        
        # 增强连通性策略说明
        print_section("📋 V2版连通性增强策略")
        print("1. 多层次桥接策略：")
        print("   - POI ↔ 建筑物 ↔ L5地块 ↔ L4区域")
        print("   - 建筑物作为不同层次间的桥梁节点")
        print("   - 减少图的直径，增强连通性")
        
        print("\n2. 距离梯度连接：")
        print(f"   - 使用{len(PARAMS['gradient_distances'])}个距离阈值: {PARAMS['gradient_distances']}")
        print("   - 不同距离建立不同类型的连接")
        print("   - 近距离：强连接，远距离：弱连接")
        
        print("\n3. 密度加权连接：")
        print(f"   - 高密度区域阈值: {PARAMS['density_enhancement_threshold']}个建筑物")
        print("   - 密集区域内部建立网状连接")
        print("   - 增强局部连通性")
        
        print("\n4. 功能聚类增强：")
        print(f"   - 功能聚类半径: {PARAMS['functional_cluster_radius']}米")
        print("   - 识别功能密集区域")
        print("   - 建立功能中心间的连接")
        
        print("\n5. 形态相似连接：")
        print("   - 相同建筑形态的地块间连接")
        print("   - 基于Spacematrix指标分类")
        print("   - 增强同质区域连接")
        
        print("\n6. 参数优化（增强连通性）：")
        print(f"   - 功能相似度阈值: {PARAMS['function_similarity_threshold']} (降低)")
        print(f"   - 便利性标准放宽: 最少{PARAMS['convenience_min_categories']}类POI")
        print(f"   - 流动距离增加: {PARAMS['flow_distance_threshold']}米")
        print(f"   - 吸引距离增加: {PARAMS['attraction_distance_threshold']}米")
        
        return 0
        
    except Exception as e:
        print(f"❌ 生成知识图谱时出错: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())