"""
增强版无建筑物知识图谱生成器
包含12种关系类型，显著提升图连通性

关系类型详解：
1. 空间关系 (6种)：
   - borderBy: 区域边界相接 (A与B共享边界)
   - nearBy: 区域距离很近 (中心点距离<300m)
   - withinBuffer: 区域在缓冲范围内 (300m<距离<800m)
   - spatialOverlap: 区域缓冲区重叠 (100m缓冲区重叠>10%)
   - locateAt: POI位于区域内 (POI包含在区域几何范围内)
   - accessibleBy: 区域间可达 (距离200m-1.5km，适合步行/短程交通)

2. 功能相似性关系 (5种)：
   - similarFunction: 功能分布相似 (POI类别分布余弦相似度>0.5)
   - poiCountSimilar: POI数量相似 (数量比值>0.6)
   - categoryDiversitySimilar: 类别多样性相似 (类别数量比值>0.7)
   - dominantFunctionSimilar: 主导功能相同 (最主要POI类别相同)
   - functionalComplement: 功能互补 (具有互补的POI类别组合)

3. 移动性关系 (3种)：
   - flowTransition: 实际人员流动 (基于签到数据，2小时内连续签到)
   - simulatedFlow: 模拟流动 (基于POI类型推断的可能流动)
   - attractionFlow: 吸引流动 (向高吸引力区域的流动)

4. 分类关系 (2种)：
   - cateOf: POI属于类别 (POI归属其主类别)
   - belongTo: POI属于商圈 (POI在商圈范围内)

5. 服务关系 (1种)：
   - provideService: 商圈服务区域 (商圈为区域提供服务)
"""

import os
import sys
import pandas as pd
import geopandas as gpd
import numpy as np
from shapely.geometry import Point
from scipy.spatial.distance import cosine
import itertools
from tqdm import tqdm
import time

# ==================== 配置部分 ====================

# 数据路径配置
DATA_PATHS = {
    "l4_shp_path": r"C:\Users\<USER>\Desktop\22\8-街区数据\沈河L4.shp",
    "poi_path": r"C:\Users\<USER>\Desktop\22\6-POI数据\merged_poi.shp",
    "bc_path": r"C:\Users\<USER>\Desktop\22\15-商圈数据\沈阳商圈.shp",
    "checkin_path": r"D:\研二\能耗估算\1-沈阳\1-数据\6-微博数据\weibo1.csv",
}

# 输出路径配置
OUTPUT_PATHS = {
    "kg_without_building": r"enhanced_output/kg_without_building_enhanced.txt",
    "category_mapping": r"enhanced_output/category_mapping.csv",
    "relation_stats": r"enhanced_output/relation_statistics.csv",
}

# 增强参数配置 - 针对连通性优化
PARAMS = {
    # === 空间关系参数 ===
    "nearby_distance": 300,        # nearBy关系距离阈值(米) - 降低以增加连接
    "buffer_distance": 800,        # withinBuffer关系距离阈值(米)
    "overlap_threshold": 0.1,      # spatialOverlap重叠比例阈值
    "accessibility_min": 200,      # accessibleBy最小距离(米)
    "accessibility_max": 1500,     # accessibleBy最大距离(米)
    
    # === 相似性参数 ===
    "function_similarity_threshold": 0.5,    # 功能相似度阈值 - 大幅降低
    "poi_count_similarity_threshold": 0.6,   # POI数量相似度阈值
    "category_diversity_threshold": 0.7,     # 类别多样性相似度阈值
    
    # === 移动性参数 ===
    "flow_time_threshold": 7200,             # 流动时间阈值(秒) - 2小时
    "flow_distance_threshold": 1000,         # 模拟流动距离阈值(米)
    "attraction_distance_threshold": 2000,   # 吸引流动距离阈值(米)
    
    # === 坐标系统 ===
    "crs": "EPSG:4326",           # WGS84坐标系
    "utm_crs": "EPSG:32651",      # UTM 51N，适合沈阳地区
    
    # === ID前缀 ===
    "region_prefix": "Region_",
    "poi_prefix": "POI_",
    "category_prefix": "Cate_",
    "bc_prefix": "BC_",
}

# 字段名映射
FIELD_MAPPING = {
    "l4": {
        "id_field": "BlockID",        # L4街区ID字段
        "geometry_field": "geometry",
    },
    "poi": {
        "name_field": "name",
        "category_field": "main_cat",
        "subcategory_field": "sub_cat",
        "geometry_field": "geometry",
    },
    "bc": {
        "id_field": "OBJECTID",
        "name_field": "Name_CHN",
        "geometry_field": "geometry",
    },
    "checkin": {
        "user_field": "账号",
        "time_field": "发布时间",
        "lon_field": "经度",
        "lat_field": "纬度",
    },
}

# 功能互补关系配置
FUNCTIONAL_COMPLEMENTS = [
    ("餐饮服务", "购物服务"),      # 餐饮与购物互补
    ("生活服务", "体育休闲服务"),  # 生活服务与休闲互补
    ("医疗保健服务", "生活服务"),  # 医疗与生活服务互补
    ("教育文化服务", "体育休闲服务"), # 教育与休闲互补
    ("交通设施服务", "商务住宅"),   # 交通与住宅互补
]

# 模拟流动模式配置 (源POI类型 -> 目标POI类型)
FLOW_PATTERNS = [
    ("交通设施服务", "商务住宅"),   # 交通枢纽到住宅
    ("餐饮服务", "购物服务"),       # 餐饮到购物
    ("教育文化服务", "餐饮服务"),   # 教育到餐饮
    ("医疗保健服务", "生活服务"),   # 医疗到生活服务
    ("体育休闲服务", "餐饮服务"),   # 休闲到餐饮
    ("商务住宅", "生活服务"),       # 住宅到生活服务
]

# 高吸引力POI类型 (用于attractionFlow)
HIGH_ATTRACTION_CATEGORIES = [
    "购物服务", "餐饮服务", "体育休闲服务", 
    "旅游景点", "教育文化服务", "交通设施服务"
]

# ==================== 工具函数 ====================

def ensure_dir(file_path):
    """确保目录存在"""
    directory = os.path.dirname(file_path)
    if not os.path.exists(directory):
        os.makedirs(directory)

def print_section(title):
    """打印分节标题"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def show_progress(iterable, desc, total=None):
    """显示进度条"""
    if total is None:
        total = len(iterable) if hasattr(iterable, '__len__') else None
    
    return tqdm(iterable, desc=desc, total=total, 
               bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}]',
               colour='green')

# ==================== 数据加载与预处理 ====================

def load_data():
    """加载所有数据文件"""
    print_section("数据加载")
    
    print("正在加载数据文件...")
    
    # 使用进度条显示数据加载进度
    data_files = [
        ("L4街区数据", DATA_PATHS["l4_shp_path"]),
        ("POI数据", DATA_PATHS["poi_path"]),
        ("商圈数据", DATA_PATHS["bc_path"]),
        ("签到数据", DATA_PATHS["checkin_path"])
    ]
    
    results = []
    
    for desc, path in show_progress(data_files, "加载数据"):
        try:
            if path.endswith('.shp'):
                data = gpd.read_file(path)
            else:
                data = pd.read_csv(path)
            results.append(data)
            tqdm.write(f"✅ {desc}: {len(data)} 条记录")
        except Exception as e:
            tqdm.write(f"❌ {desc} 加载失败: {e}")
            results.append(pd.DataFrame())  # 空DataFrame作为占位符
    
    return tuple(results)

def preprocess_data(l4_gdf, poi_gdf, bc_gdf, checkin_df):
    """数据预处理：统一坐标系，添加ID，空间关联"""
    print_section("数据预处理")
    
    # 1. 统一坐标系
    print("统一坐标系到UTM...")
    utm_crs = PARAMS["utm_crs"]
    l4_gdf = l4_gdf.to_crs(utm_crs)
    poi_gdf = poi_gdf.to_crs(utm_crs)
    bc_gdf = bc_gdf.to_crs(utm_crs)
    
    # 2. 添加统一ID
    print("生成统一ID...")
    
    # L4区域ID
    l4_gdf.columns = l4_gdf.columns.str.lower()
    id_field = FIELD_MAPPING["l4"]["id_field"].lower()
    
    if id_field in l4_gdf.columns:
        l4_gdf["region_id"] = l4_gdf[id_field].apply(
            lambda x: f"{PARAMS['region_prefix']}{x}"
        )
        print(f"✅ 使用字段 '{id_field}' 生成区域ID")
    else:
        l4_gdf["region_id"] = l4_gdf.index.map(
            lambda x: f"{PARAMS['region_prefix']}{x}"
        )
        print(f"⚠️ 字段 '{id_field}' 不存在，使用索引生成区域ID")
    
    # POI ID
    poi_gdf["poi_id"] = poi_gdf.index.map(lambda x: f"{PARAMS['poi_prefix']}{x}")
    
    # 类别ID映射
    unique_cats = poi_gdf['main_cat'].dropna().unique()
    category_mapping = {cat: f"{PARAMS['category_prefix']}{i}" 
                       for i, cat in enumerate(unique_cats)}
    poi_gdf["category_id"] = poi_gdf['main_cat'].map(
        lambda x: category_mapping.get(x, f"{PARAMS['category_prefix']}_None") 
        if pd.notna(x) else f"{PARAMS['category_prefix']}_None"
    )
    
    # 保存类别映射
    ensure_dir(OUTPUT_PATHS["category_mapping"])
    pd.DataFrame({
        'main_cat': list(category_mapping.keys()),
        'category_id': list(category_mapping.values())
    }).to_csv(OUTPUT_PATHS["category_mapping"], index=False, encoding='utf-8')
    
    # 商圈ID
    bc_gdf["bc_id"] = bc_gdf["OBJECTID"].apply(
        lambda x: f"{PARAMS['bc_prefix']}{int(x)}" if pd.notna(x) else "BC_0"
    )
    
    # 3. 处理签到数据
    print("处理签到数据...")
    try:
        checkin_df["经度"] = pd.to_numeric(checkin_df["经度"], errors='coerce')
        checkin_df["纬度"] = pd.to_numeric(checkin_df["纬度"], errors='coerce')
        
        # 过滤有效坐标
        valid_coords = (
            (checkin_df["经度"] >= 120.0) & (checkin_df["经度"] <= 130.0) &
            (checkin_df["纬度"] >= 38.0) & (checkin_df["纬度"] <= 45.0)
        )
        checkin_df = checkin_df[valid_coords]
        
        # 转换为GeoDataFrame
        geometry = [Point(xy) for xy in zip(checkin_df["经度"], checkin_df["纬度"])]
        checkin_gdf = gpd.GeoDataFrame(checkin_df, geometry=geometry, crs="EPSG:4326")
        checkin_gdf = checkin_gdf.to_crs(utm_crs)
        
        print(f"✅ 有效签到数据: {len(checkin_gdf)} 条")
    except Exception as e:
        print(f"⚠️ 签到数据处理失败: {e}")
        checkin_gdf = gpd.GeoDataFrame()
    
    # 4. 空间关联
    print("执行空间关联...")
    
    # POI与区域关联
    print("  POI与区域空间关联...")
    poi_gdf = gpd.sjoin(poi_gdf, l4_gdf[["geometry", "region_id"]], 
                       how='left', predicate='within')
    poi_gdf.rename(columns={"index_right": "street_index"}, inplace=True)
    
    # POI与商圈关联
    print("  POI与商圈空间关联...")
    poi_gdf = gpd.sjoin(poi_gdf, bc_gdf[["geometry", "bc_id"]], 
                       how='left', predicate='within')
    poi_gdf.rename(columns={"index_right": "bc_index"}, inplace=True)
    
    # 签到与区域关联
    if not checkin_gdf.empty:
        print("  签到与区域空间关联...")
        try:
            checkin_with_region = gpd.sjoin(checkin_gdf, l4_gdf[["geometry", "region_id"]], 
                                          how='left', predicate='within')
            checkin_gdf["region_id"] = checkin_with_region["region_id"]
            matched = checkin_gdf["region_id"].notna().sum()
            print(f"✅ 签到匹配结果: {matched}/{len(checkin_gdf)} 条匹配到区域")
        except Exception as e:
            print(f"⚠️ 签到空间关联失败: {e}")
    
    # 5. 数据过滤
    print("根据L4边界过滤数据...")
    l4_boundary = l4_gdf.unary_union
    
    # 显示过滤进度
    filter_tasks = [
        ("POI数据", poi_gdf),
        ("商圈数据", bc_gdf)
    ]
    
    filtered_results = []
    for desc, data in show_progress(filter_tasks, "过滤数据"):
        before_count = len(data)
        filtered_data = data[data.intersects(l4_boundary)]
        after_count = len(filtered_data)
        filtered_results.append(filtered_data)
        
        percentage = after_count/before_count*100 if before_count > 0 else 0
        tqdm.write(f"✅ {desc}: {after_count}/{before_count} ({percentage:.1f}%)")
    
    poi_gdf, bc_gdf = filtered_results
    
    return l4_gdf, poi_gdf, bc_gdf, checkin_gdf

# ==================== 关系生成函数 ====================

def generate_spatial_relations(l4_gdf, poi_gdf):
    """
    生成空间关系 (6种)
    
    关系说明：
    - borderBy: 区域A与区域B的边界相接触
    - nearBy: 区域中心点距离 < 300米
    - withinBuffer: 区域中心点距离在300-800米之间
    - spatialOverlap: 区域100米缓冲区重叠 > 10%
    - locateAt: POI位于区域几何范围内
    - accessibleBy: 区域间距离200米-1.5公里 (步行/短程交通可达)
    """
    print_section("生成空间关系")
    triples = []
    
    # 计算区域中心点
    l4_centroids = l4_gdf.copy()
    l4_centroids.geometry = l4_centroids.geometry.centroid
    
    print("1. borderBy - 边界相接关系...")
    count = 0
    region_pairs = [(i, j, row1, row2) for i, row1 in enumerate(l4_gdf.itertuples()) 
                   for j, row2 in enumerate(l4_gdf.itertuples()) if i < j]
    
    for i, j, row1, row2 in show_progress(region_pairs, "边界检测"):
        if row1.geometry.touches(row2.geometry):
            triples.append((row1.region_id, "borderBy", row2.region_id))
            count += 1
    
    print(f"   生成 {count} 个 borderBy 三元组")
    
    print("2. nearBy - 近距离关系...")
    count = 0
    centroid_pairs = [(i, j, row1, row2) for i, row1 in enumerate(l4_centroids.itertuples()) 
                     for j, row2 in enumerate(l4_centroids.itertuples()) if i < j]
    
    for i, j, row1, row2 in show_progress(centroid_pairs, "近距离计算"):
        distance = row1.geometry.distance(row2.geometry)
        if distance < PARAMS["nearby_distance"]:
            triples.append((row1.region_id, "nearBy", row2.region_id))
            count += 1
    
    print(f"   生成 {count} 个 nearBy 三元组")
    
    print("3. withinBuffer - 缓冲区内关系...")
    count = 0
    for i, j, row1, row2 in show_progress(centroid_pairs, "缓冲区计算"):
        distance = row1.geometry.distance(row2.geometry)
        if PARAMS["nearby_distance"] < distance < PARAMS["buffer_distance"]:
            triples.append((row1.region_id, "withinBuffer", row2.region_id))
            count += 1
    
    print(f"   生成 {count} 个 withinBuffer 三元组")
    
    print("4. spatialOverlap - 空间重叠关系...")
    count = 0
    l4_buffered = l4_gdf.copy()
    l4_buffered.geometry = l4_buffered.geometry.buffer(100)  # 100米缓冲区
    
    buffered_pairs = [(i, j, row1, row2) for i, row1 in enumerate(l4_buffered.itertuples()) 
                     for j, row2 in enumerate(l4_buffered.itertuples()) if i < j]
    
    for i, j, row1, row2 in show_progress(buffered_pairs, "重叠计算"):
        intersection = row1.geometry.intersection(row2.geometry)
        if not intersection.is_empty:
            overlap_ratio = intersection.area / min(row1.geometry.area, row2.geometry.area)
            if overlap_ratio > PARAMS["overlap_threshold"]:
                triples.append((row1.region_id, "spatialOverlap", row2.region_id))
                count += 1
    
    print(f"   生成 {count} 个 spatialOverlap 三元组")
    
    print("5. locateAt - POI位置关系...")
    count = 0
    for _, row in show_progress(poi_gdf.iterrows(), "POI位置"):
        if pd.notna(row.region_id):
            triples.append((row.poi_id, "locateAt", row.region_id))
            count += 1
    
    print(f"   生成 {count} 个 locateAt 三元组")
    
    print("6. accessibleBy - 可达性关系...")
    count = 0
    for i, j, row1, row2 in show_progress(centroid_pairs, "可达性计算"):
        distance = row1.geometry.distance(row2.geometry)
        if PARAMS["accessibility_min"] < distance < PARAMS["accessibility_max"]:
            triples.append((row1.region_id, "accessibleBy", row2.region_id))
            count += 1
    
    print(f"   生成 {count} 个 accessibleBy 三元组")
    
    print(f"✅ 空间关系总计: {len(triples)} 个三元组")
    return triples

def calculate_poi_distribution(l4_gdf, poi_gdf):
    """计算每个区域的POI类别分布向量"""
    all_categories = poi_gdf["main_cat"].dropna().unique()
    category_to_idx = {cat: i for i, cat in enumerate(all_categories)}
    
    distributions = {}
    for _, region in l4_gdf.iterrows():
        region_pois = poi_gdf[poi_gdf.region_id == region.region_id]
        distribution = np.zeros(len(all_categories))
        
        if not region_pois.empty:
            for _, poi in region_pois.iterrows():
                category = poi.main_cat
                if pd.notna(category) and category in category_to_idx:
                    distribution[category_to_idx[category]] += 1
            
            if distribution.sum() > 0:
                distribution = distribution / distribution.sum()
        
        distributions[region.region_id] = distribution
    
    return distributions, all_categories

def generate_similarity_relations(l4_gdf, poi_gdf):
    """
    生成功能相似性关系 (5种)
    
    关系说明：
    - similarFunction: POI类别分布余弦相似度 ≥ 0.5
    - poiCountSimilar: POI总数比值 ≥ 0.6
    - categoryDiversitySimilar: POI类别数量比值 ≥ 0.7
    - dominantFunctionSimilar: 最主要的POI类别相同
    - functionalComplement: 具有预定义的互补POI类别组合
    """
    print_section("生成功能相似性关系")
    triples = []
    
    # 计算POI分布
    distributions, _ = calculate_poi_distribution(l4_gdf, poi_gdf)
    region_ids = list(distributions.keys())
    
    print("1. similarFunction - 功能分布相似...")
    count = 0
    vectors = np.array([distributions[rid] for rid in region_ids])
    
    if len(vectors) > 0 and vectors.shape[1] > 0:
        region_pairs = [(i, j) for i in range(len(region_ids)) for j in range(i+1, len(region_ids))]
        
        for i, j in show_progress(region_pairs, "功能相似度"):
            if np.sum(vectors[i]) > 0 and np.sum(vectors[j]) > 0:
                similarity = 1 - cosine(vectors[i], vectors[j])
                if similarity >= PARAMS["function_similarity_threshold"]:
                    triples.append((region_ids[i], "similarFunction", region_ids[j]))
                    count += 1
    
    print(f"   生成 {count} 个 similarFunction 三元组")
    
    print("2. poiCountSimilar - POI数量相似...")
    count = 0
    poi_counts = {}
    
    for _, region in show_progress(l4_gdf.iterrows(), "计算POI数量"):
        poi_counts[region.region_id] = len(poi_gdf[poi_gdf.region_id == region.region_id])
    
    region_pairs = [(i, j) for i in range(len(region_ids)) for j in range(i+1, len(region_ids))]
    for i, j in show_progress(region_pairs, "POI数量相似度"):
        count1 = poi_counts.get(region_ids[i], 0)
        count2 = poi_counts.get(region_ids[j], 0)
        
        if count1 > 0 and count2 > 0:
            ratio = min(count1, count2) / max(count1, count2)
            if ratio >= PARAMS["poi_count_similarity_threshold"]:
                triples.append((region_ids[i], "poiCountSimilar", region_ids[j]))
                count += 1
    
    print(f"   生成 {count} 个 poiCountSimilar 三元组")
    
    print("3. categoryDiversitySimilar - 类别多样性相似...")
    count = 0
    category_diversity = {}
    
    for _, region in show_progress(l4_gdf.iterrows(), "计算类别多样性"):
        region_pois = poi_gdf[poi_gdf.region_id == region.region_id]
        category_diversity[region.region_id] = region_pois['main_cat'].dropna().nunique()
    
    for i, j in show_progress(region_pairs, "类别多样性相似度"):
        div1 = category_diversity.get(region_ids[i], 0)
        div2 = category_diversity.get(region_ids[j], 0)
        
        if div1 > 0 and div2 > 0:
            ratio = min(div1, div2) / max(div1, div2)
            if ratio >= PARAMS["category_diversity_threshold"]:
                triples.append((region_ids[i], "categoryDiversitySimilar", region_ids[j]))
                count += 1
    
    print(f"   生成 {count} 个 categoryDiversitySimilar 三元组")
    
    print("4. dominantFunctionSimilar - 主导功能相似...")
    count = 0
    dominant_functions = {}
    
    for _, region in show_progress(l4_gdf.iterrows(), "计算主导功能"):
        region_pois = poi_gdf[poi_gdf.region_id == region.region_id]
        if len(region_pois) > 0:
            dominant_cat = region_pois['main_cat'].mode()
            if len(dominant_cat) > 0:
                dominant_functions[region.region_id] = dominant_cat.iloc[0]
    
    for i, j in show_progress(region_pairs, "主导功能相似度"):
        func1 = dominant_functions.get(region_ids[i])
        func2 = dominant_functions.get(region_ids[j])
        
        if func1 and func2 and func1 == func2:
            triples.append((region_ids[i], "dominantFunctionSimilar", region_ids[j]))
            count += 1
    
    print(f"   生成 {count} 个 dominantFunctionSimilar 三元组")
    
    print("5. functionalComplement - 功能互补...")
    count = 0
    
    # 预计算每个区域的POI类别集合
    region_categories = {}
    for _, region in show_progress(l4_gdf.iterrows(), "预计算POI类别"):
        region_pois = poi_gdf[poi_gdf.region_id == region.region_id]
        region_categories[region.region_id] = set(region_pois['main_cat'].dropna())
    
    complement_tasks = [(source_cat, target_cat, region1.region_id, region2.region_id) 
                       for source_cat, target_cat in FUNCTIONAL_COMPLEMENTS 
                       for _, region1 in l4_gdf.iterrows() 
                       for _, region2 in l4_gdf.iterrows() 
                       if region1.region_id != region2.region_id]
    
    for source_cat, target_cat, region1_id, region2_id in show_progress(complement_tasks, "功能互补检测"):
        region1_cats = region_categories.get(region1_id, set())
        region2_cats = region_categories.get(region2_id, set())
        
        if source_cat in region1_cats and target_cat in region2_cats:
            triples.append((region1_id, "functionalComplement", region2_id))
            count += 1
            # 避免重复检测同一对区域
            break
    
    print(f"   生成 {count} 个 functionalComplement 三元组")
    
    print(f"✅ 相似性关系总计: {len(triples)} 个三元组")
    return triples

def generate_mobility_relations(l4_gdf, poi_gdf, checkin_gdf):
    """
    生成移动性关系 (3种)
    
    关系说明：
    - flowTransition: 基于签到数据的实际人员流动 (2小时内连续签到)
    - simulatedFlow: 基于POI类型推断的可能流动模式
    - attractionFlow: 向高吸引力区域的流动 (基于POI吸引力)
    """
    print_section("生成移动性关系")
    triples = []
    
    print("1. flowTransition - 实际流动...")
    count = 0
    if not checkin_gdf.empty and "发布时间" in checkin_gdf.columns:
        try:
            checkin_gdf["timestamp"] = pd.to_datetime(checkin_gdf["发布时间"])
            
            if pd.notna(checkin_gdf["timestamp"]).any():
                user_groups = list(checkin_gdf.groupby("账号"))
                
                for _, group in show_progress(user_groups, "用户流动分析"):
                    group = group.sort_values("timestamp")
                    
                    for i in range(len(group) - 1):
                        current = group.iloc[i]
                        next_checkin = group.iloc[i+1]
                        
                        if pd.notna(current.region_id) and pd.notna(next_checkin.region_id):
                            time_diff = (next_checkin.timestamp - current.timestamp).total_seconds()
                            
                            if (time_diff < PARAMS["flow_time_threshold"] and 
                                current.region_id != next_checkin.region_id):
                                triples.append((current.region_id, "flowTransition", next_checkin.region_id))
                                count += 1
        except Exception as e:
            print(f"   ⚠️ 签到数据处理失败: {e}")
    
    print(f"   生成 {count} 个 flowTransition 三元组")
    
    print("2. simulatedFlow - 模拟流动...")
    count = 0
    l4_centroids = l4_gdf.copy()
    l4_centroids.geometry = l4_centroids.geometry.centroid
    
    # 预计算每个区域的POI类别
    region_categories = {}
    for _, region in show_progress(l4_gdf.iterrows(), "预计算区域类别"):
        region_pois = poi_gdf[poi_gdf.region_id == region.region_id]
        region_categories[region.region_id] = set(region_pois['main_cat'].dropna())
    
    for source_cat, target_cat in show_progress(FLOW_PATTERNS, "分析流动模式"):
        source_regions = [rid for rid, cats in region_categories.items() if source_cat in cats]
        target_regions = [rid for rid, cats in region_categories.items() if target_cat in cats]
        
        flow_pairs = [(s, t) for s in source_regions for t in target_regions if s != t]
        
        for source_id, target_id in flow_pairs:
            source_geom = l4_gdf[l4_gdf.region_id == source_id].geometry.centroid.iloc[0]
            target_geom = l4_gdf[l4_gdf.region_id == target_id].geometry.centroid.iloc[0]
            distance = source_geom.distance(target_geom)
            
            if distance < PARAMS["flow_distance_threshold"]:
                triples.append((source_id, "simulatedFlow", target_id))
                count += 1
    
    print(f"   生成 {count} 个 simulatedFlow 三元组")
    
    print("3. attractionFlow - 吸引流动...")
    count = 0
    
    # 计算区域吸引力得分
    attraction_scores = {}
    for _, region in show_progress(l4_gdf.iterrows(), "计算吸引力得分"):
        region_pois = poi_gdf[poi_gdf.region_id == region.region_id]
        score = 0
        
        for cat in HIGH_ATTRACTION_CATEGORIES:
            score += len(region_pois[region_pois['main_cat'] == cat])
        
        if score > 0:
            attraction_scores[region.region_id] = score
    
    # 生成吸引流动关系
    attraction_pairs = [(target_id, source_region.region_id) 
                       for target_id in attraction_scores.keys() 
                       for _, source_region in l4_gdf.iterrows() 
                       if source_region.region_id != target_id]
    
    for target_id, source_id in show_progress(attraction_pairs, "生成吸引流动"):
        target_geom = l4_gdf[l4_gdf.region_id == target_id].geometry.centroid.iloc[0]
        source_geom = l4_gdf[l4_gdf.region_id == source_id].geometry.centroid.iloc[0]
        distance = source_geom.distance(target_geom)
        
        if distance < PARAMS["attraction_distance_threshold"]:
            attraction_score = attraction_scores[target_id]
            attraction_prob = attraction_score / (1 + distance / 500)
            if attraction_prob > 2:
                triples.append((source_id, "attractionFlow", target_id))
                count += 1
    
    print(f"   生成 {count} 个 attractionFlow 三元组")
    
    print(f"✅ 移动性关系总计: {len(triples)} 个三元组")
    return triples

def generate_categorical_relations(poi_gdf, bc_gdf):
    """
    生成分类关系 (2种)
    
    关系说明：
    - cateOf: POI属于其主类别
    - belongTo: POI属于商圈 (在商圈几何范围内)
    """
    print_section("生成分类关系")
    triples = []
    
    print("1. cateOf - POI类别归属...")
    count = 0
    for _, row in show_progress(poi_gdf.iterrows(), "POI类别"):
        if pd.notna(row.category_id) and pd.notna(row.poi_id):
            triples.append((row.poi_id, "cateOf", row.category_id))
            count += 1
    
    print(f"   生成 {count} 个 cateOf 三元组")
    
    print("2. belongTo - POI商圈归属...")
    count = 0
    for _, row in show_progress(poi_gdf.iterrows(), "POI商圈"):
        if pd.notna(row.bc_id):
            triples.append((row.poi_id, "belongTo", row.bc_id))
            count += 1
    
    print(f"   生成 {count} 个 belongTo 三元组")
    
    print(f"✅ 分类关系总计: {len(triples)} 个三元组")
    return triples

def generate_service_relations(l4_gdf, bc_gdf):
    """
    生成服务关系 (1种)
    
    关系说明：
    - provideService: 商圈为区域提供服务 (商圈包含区域中心或与区域相交)
    """
    print_section("生成服务关系")
    triples = []
    
    print("1. provideService - 商圈服务...")
    count = 0
    
    service_pairs = [(bc.Index, region.Index, bc, region) 
                    for bc in bc_gdf.itertuples() 
                    for region in l4_gdf.itertuples()]
    
    for bc_idx, region_idx, bc, region in show_progress(service_pairs, "商圈服务检测"):
        # 商圈包含区域中心点或与区域相交
        if (bc.geometry.contains(region.geometry.centroid) or 
            bc.geometry.intersects(region.geometry)):
            triples.append((bc.bc_id, "provideService", region.region_id))
            count += 1
    
    print(f"   生成 {count} 个 provideService 三元组")
    
    print(f"✅ 服务关系总计: {len(triples)} 个三元组")
    return triples

# ==================== 主函数 ====================

def save_triples_with_stats(triples, output_path):
    """保存三元组并生成统计信息"""
    print_section("保存三元组和统计")
    
    print("去重三元组...")
    unique_triples = list(set(triples))
    print(f"去重前: {len(triples):,} 个三元组")
    print(f"去重后: {len(unique_triples):,} 个三元组")
    
    # 保存三元组
    print("保存三元组到文件...")
    ensure_dir(output_path)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        for head, relation, tail in show_progress(unique_triples, "写入三元组"):
            f.write(f"{head}\t{relation}\t{tail}\n")
    
    # 统计关系类型
    print("统计关系类型...")
    relation_counts = {}
    for _, relation, _ in show_progress(unique_triples, "统计关系"):
        relation_counts[relation] = relation_counts.get(relation, 0) + 1
    
    # 保存统计信息
    print("生成统计报告...")
    stats_df = pd.DataFrame([
        {'relation_type': rel, 'count': count, 'percentage': count/len(unique_triples)*100}
        for rel, count in sorted(relation_counts.items(), key=lambda x: x[1], reverse=True)
    ])
    
    ensure_dir(OUTPUT_PATHS["relation_stats"])
    stats_df.to_csv(OUTPUT_PATHS["relation_stats"], index=False)
    
    # 打印统计信息
    print_section("知识图谱统计")
    print(f"三元组总数: {len(unique_triples):,}")
    print(f"关系类型数: {len(relation_counts)}")
    print("\n各关系类型分布:")
    for _, row in stats_df.iterrows():
        print(f"  {row['relation_type']:<20}: {row['count']:>6,} ({row['percentage']:>5.1f}%)")
    
    return unique_triples, relation_counts

def estimate_connectivity(triples, region_count):
    """估算图连通性"""
    print_section("连通性分析")
    
    # 区域间连接关系
    spatial_relations = ['borderBy', 'nearBy', 'withinBuffer', 'spatialOverlap', 'accessibleBy']
    similarity_relations = ['similarFunction', 'poiCountSimilar', 'categoryDiversitySimilar', 
                          'dominantFunctionSimilar', 'functionalComplement']
    mobility_relations = ['flowTransition', 'simulatedFlow', 'attractionFlow']
    
    spatial_count = len([t for t in triples if t[1] in spatial_relations])
    similarity_count = len([t for t in triples if t[1] in similarity_relations])
    mobility_count = len([t for t in triples if t[1] in mobility_relations])
    
    # 估算每个区域的平均连接数
    total_region_connections = (spatial_count + similarity_count + mobility_count) * 2
    avg_connections = total_region_connections / region_count if region_count > 0 else 0
    
    print(f"区域总数: {region_count}")
    print(f"空间连接: {spatial_count:,}")
    print(f"相似性连接: {similarity_count:,}")
    print(f"移动性连接: {mobility_count:,}")
    print(f"估算平均每区域连接数: {avg_connections:.2f}")
    
    if avg_connections > 8:
        print("✅ 连通性优秀！图结构非常适合GNN模型")
    elif avg_connections > 5:
        print("✅ 连通性良好！预期解决连通性问题")
    elif avg_connections > 3:
        print("⚠️ 连通性一般，可能需要进一步优化")
    else:
        print("❌ 连通性仍然较差，建议检查参数设置")

def main():
    """主函数"""
    try:
        print_section("🚀 增强版无建筑物知识图谱生成器启动")
        start_time = time.time()
        
        # 1. 数据加载
        l4_gdf, poi_gdf, bc_gdf, checkin_df = load_data()
        
        # 2. 数据预处理
        l4_gdf, poi_gdf, bc_gdf, checkin_gdf = preprocess_data(l4_gdf, poi_gdf, bc_gdf, checkin_df)
        
        # 3. 生成各类关系
        all_triples = []
        relation_counts = {}
        
        print_section("🔗 开始生成知识图谱关系")
        
        relation_tasks = [
            ("空间关系", lambda: generate_spatial_relations(l4_gdf, poi_gdf)),
            ("相似性关系", lambda: generate_similarity_relations(l4_gdf, poi_gdf)),
            ("移动性关系", lambda: generate_mobility_relations(l4_gdf, poi_gdf, checkin_gdf)),
            ("分类关系", lambda: generate_categorical_relations(poi_gdf, bc_gdf)),
            ("服务关系", lambda: generate_service_relations(l4_gdf, bc_gdf))
        ]
        
        for task_name, task_func in show_progress(relation_tasks, "生成关系类型"):
            task_start = time.time()
            triples = task_func()
            all_triples.extend(triples)
            task_time = time.time() - task_start
            
            tqdm.write(f"✅ {task_name}: {len(triples):,} 个三元组 (耗时: {task_time:.1f}s)")
        
        # 4. 保存结果并统计
        unique_triples, final_relation_counts = save_triples_with_stats(all_triples, OUTPUT_PATHS["kg_without_building"])
        
        # 5. 连通性分析
        estimate_connectivity(unique_triples, len(l4_gdf))
        
        # 6. 总结
        total_time = time.time() - start_time
        print_section("🎉 生成完成")
        print(f"✅ 总耗时: {total_time:.1f} 秒")
        print(f"✅ 处理速度: {len(unique_triples)/total_time:.0f} 三元组/秒")
        print(f"✅ 知识图谱已保存: {OUTPUT_PATHS['kg_without_building']}")
        print(f"✅ 统计信息已保存: {OUTPUT_PATHS['relation_stats']}")
        print(f"✅ 类别映射已保存: {OUTPUT_PATHS['category_mapping']}")
        
        return 0
        
    except Exception as e:
        print(f"❌ 生成知识图谱时出错: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())