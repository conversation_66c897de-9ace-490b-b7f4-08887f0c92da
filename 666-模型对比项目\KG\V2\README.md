# 增强版含建筑物知识图谱生成器

## 项目简介

本项目是一个基于多源城市空间数据的知识图谱生成器，专门用于构建包含建筑物、地块、区域、POI等多层次城市要素的复杂知识图谱。该系统支持19种不同类型的空间关系，能够深入分析城市空间结构、功能分布和要素间的复杂关联。

## 主要特性

### 🏗️ 多层次空间建模
- **L4街区**: 城市宏观区域单元
- **L5地块**: 中观土地利用单元  
- **建筑物**: 微观建筑要素
- **POI**: 兴趣点和服务设施
- **土地利用**: 基于Level2_cn字段的用地分类

### 🔗 丰富的关系类型 (19种)

#### 空间关系 (5种)
- `borderBy`: 区域边界相接关系
- `nearBy`: 区域近距离关系
- `connectedTo`: 建筑物连接关系
- `locateAt`: POI位置关系
- `withinRegion`: 建筑物区域归属

#### 功能相似性关系 (3种)
- `similarFunction`: 区域功能相似性
- `highConvenience`: 高便利性区域关联
- `morphologySimilar`: 地块形态相似性

#### 移动性关系 (2种)
- `flowTransition`: 综合人员流动关系
- `densityInfluences`: 密度影响关系 ⭐️**新增**

#### 分类关系 (2种)
- `cateOf`: POI类别归属
- `belongTo`: POI商圈归属

#### 服务关系 (1种)
- `provideService`: 商圈服务关系

#### 建筑物关系 (4种)
- `belongsToBuilding`: 建筑物归属地块
- `belongsToLand`: 地块归属区域
- `hasFunctionBuilding`: 建筑物功能分类
- `hasMorphology`: 地块形态分类

#### 土地利用关系 (1种) ⭐️**新增**
- `hasLandUse`: 地块土地利用类型

#### 地块关系 (1种) ⭐️**新增**
- `adjacentToLand`: 地块邻接关系

## 数据要求

### 必需数据文件
```
📁 数据目录结构
├── L4街区数据.shp          # L4街区边界
├── L5地块数据.shp          # L5地块边界
├── 建筑物数据.shp          # 建筑物几何和属性
├── POI数据.shp            # 兴趣点数据
├── 商圈数据.shp           # 商圈边界
├── 土地利用数据.shp        # 土地利用分类 ⭐️**新增**
└── 签到数据.csv           # 用户签到轨迹(可选)
```

### 关键字段要求

#### 建筑物数据
- `Function`: 建筑物功能类型(英文)
- `Height`: 建筑物高度(米)
- `Age`: 建筑物年龄(可选)

#### 土地利用数据 ⭐️**重要**
- `Level2_cn`: 土地利用类型(中文) - **必需字段**

#### POI数据
- `main_cat`: POI主类别
- `sub_cat`: POI子类别
- `name`: POI名称

## 安装和使用

### 环境要求
```bash
Python >= 3.8
geopandas >= 0.10.0
pandas >= 1.3.0
numpy >= 1.21.0
shapely >= 1.8.0
scipy >= 1.7.0
tqdm >= 4.62.0
```

### 安装依赖
```bash
pip install geopandas pandas numpy shapely scipy tqdm
```

### 配置数据路径
编辑 `enhanced_kg_with_building_v3.py` 中的 `DATA_PATHS` 配置：

```python
DATA_PATHS = {
    "l4_shp_path": r"你的L4数据路径.shp",
    "l5_shp_path": r"你的L5数据路径.shp", 
    "poi_path": r"你的POI数据路径.shp",
    "bc_path": r"你的商圈数据路径.shp",
    "checkin_path": r"你的签到数据路径.csv",
    "building_path": r"你的建筑物数据路径.shp",
    "land_use_path": r"你的土地利用数据路径.shp",  # ⭐️新增
}
```

### 运行程序
```bash
python enhanced_kg_with_building_v3.py
```

## 快速开始示例

### 1. 准备示例数据
确保您有以下数据文件：
```
📁 示例数据/
├── shenhe_l4.shp              # 沈河区L4街区
├── shenhe_l5.shp              # 沈河区L5地块
├── shenyang_buildings.shp      # 沈阳建筑物
├── merged_poi.shp             # 合并POI数据
├── shenyang_bc.shp            # 沈阳商圈
├── shenyang_landuse.shp       # 沈阳土地利用 ⭐️
└── weibo1.csv                 # 微博签到数据
```

### 2. 配置数据路径
```python
# 修改 enhanced_kg_with_building_v3.py 中的路径
DATA_PATHS = {
    "l4_shp_path": r"示例数据/shenhe_l4.shp",
    "l5_shp_path": r"示例数据/shenhe_l5.shp",
    "poi_path": r"示例数据/merged_poi.shp",
    "bc_path": r"示例数据/shenyang_bc.shp",
    "checkin_path": r"示例数据/weibo1.csv",
    "building_path": r"示例数据/shenyang_buildings.shp",
    "land_use_path": r"示例数据/shenyang_landuse.shp",  # ⭐️新增
}
```

### 3. 运行并查看结果
```bash
# 运行程序
python enhanced_kg_with_building_v3.py

# 查看输出文件
ls 建筑物/
# kg_with_building_fixed.txt      - 主要三元组文件
# category_mapping.csv            - POI类别映射
# relation_statistics.csv         - 关系统计
# morphology_statistics.csv       - 形态统计
# connectivity_analysis.csv       - 连通性分析
```

### 4. 示例输出
```
# 三元组示例 (kg_with_building_fixed.txt)
Region_101    borderBy    Region_102
Building_5001    connectedTo    Building_5002
Land_1001    hasLandUse    LandUse_Residential
Region_101    densityInfluences    Region_108
Land_1001    adjacentToLand    Land_1002
POI_12345    locateAt    Region_101
Building_5001    hasFunctionBuilding    Func_Residential
Land_1001    hasMorphology    Morph_LowRiseLowDensity
```

## 核心算法

### 🏢 密度影响关系算法 ⭐️**新增**
基于城市密度梯度衰减定律，计算高密度区域对低密度区域的影响：

```python
# 复合密度指数计算
composite_density = building_count * 0.6 + building_density * 0.4

# 影响条件
if (density_ratio >= 1.5 and distance < 800m):
    create_relation(high_region, "densityInfluences", low_region)
```

### 🗺️ 地块邻接检测算法 ⭐️**新增**
使用几何拓扑关系检测地块间的物理邻接：

```python
# 精确邻接检测
if land1.geometry.touches(land2.geometry):
    create_relation(land1, "adjacentToLand", land2)
    
# 容错处理(1米缓冲)
elif land1.buffer(1m).intersects(land2.geometry):
    create_relation(land1, "adjacentToLand", land2)
```

### 🏗️ Spacematrix形态分类
基于建筑高度推算楼层数，计算FSI、GSI、OSR、L指标：

```python
floors = height / 3.3  # 层高3.3米
FSI = total_floor_area / land_area
GSI = building_area / land_area  
OSR = (1 - GSI) / FSI
L = average_floors
```

## 输出结果

### 知识图谱文件
- `kg_with_building_fixed.txt`: 主要三元组文件
- `category_mapping.csv`: POI类别映射表
- `relation_statistics.csv`: 关系统计信息
- `morphology_statistics.csv`: 形态分布统计
- `connectivity_analysis.csv`: 连通性分析报告

### 三元组格式
```
头实体    关系类型    尾实体
Region_101    borderBy    Region_102
Building_5001    connectedTo    Building_5002
Land_1001    hasLandUse    LandUse_Residential
Region_101    densityInfluences    Region_108
Land_1001    adjacentToLand    Land_1002
```

## 性能优化

### 🚀 处理效率
- **地块邻接**: 限制最多500个地块以提高效率
- **建筑物连接**: 高密度区域内网状连接
- **空间索引**: 使用GeoPandas空间连接优化
- **进度显示**: 详细的处理进度和时间统计

### 📊 预期性能
- **关系类型**: 19种
- **连通性**: 平均每实体>15个连接
- **处理速度**: 约1000-5000三元组/秒
- **内存优化**: 分批处理大规模数据

## 应用场景

### 🏙️ 城市规划分析
- 土地利用结构分析
- 建筑密度分布研究
- 功能区划识别

### 🔬 学术研究
- 城市空间网络建模
- GNN图神经网络训练
- 空间关系挖掘

### 💼 商业应用
- 选址分析
- 市场研究
- 房地产评估

## 更新日志

### V3版本新增功能 ⭐️
1. **土地利用关系**: 支持Level2_cn字段的用地分类
2. **密度影响关系**: 基于城市密度梯度理论的影响建模
3. **地块邻接关系**: 精确的地块物理邻接检测
4. **性能优化**: 提升大规模数据处理效率
5. **文档完善**: 详细的算法说明和使用指南

## 参数配置

### 核心参数说明
```python
PARAMS = {
    # 空间关系参数
    "nearby_distance": 300,                    # nearBy关系距离阈值(米)
    "buffer_distance": 800,                    # withinBuffer关系距离阈值(米)
    "building_connection_distance": 100,       # 建筑物连接距离(米)

    # 密度影响参数 ⭐️新增
    "density_influence_threshold": 1.5,        # 密度影响阈值(高/低密度比值)
    "density_influence_distance": 800,         # 密度影响最大距离(米)

    # 地块邻接参数 ⭐️新增
    "land_adjacency_buffer": 1,                # 地块邻接缓冲距离(米)

    # 功能相似性参数
    "function_similarity_threshold": 0.35,     # 功能相似度阈值
    "convenience_min_categories": 6,           # 便利性最少POI类别数

    # 形态分类参数
    "floor_height": 3.3,                       # 层高估计值(米)
    "fsi_thresholds": [0.3, 0.6, 1.2, 2.0],  # 容积率阈值
}
```

## 常见问题 FAQ

### Q1: 土地利用数据中没有Level2_cn字段怎么办？
**A**: 请检查您的土地利用数据字段名，常见的字段名包括：
- `Level2_cn` (推荐)
- `用地性质`
- `土地类型`
- `LANDUSE_TYPE`

可以在 `FIELD_MAPPING["land_use"]["type_field"]` 中修改字段名。

### Q2: 程序运行很慢怎么办？
**A**: 可以通过以下方式优化：
1. 减少地块数量限制（修改 `max_lands` 参数）
2. 调整距离阈值减少计算量
3. 使用更强的硬件配置
4. 分批处理大规模数据

### Q3: 建筑物功能映射不正确？
**A**: 请检查建筑物数据中的Function字段值，确保与 `BUILDING_FUNCTION_MAP` 中的键值匹配。常见值包括：
- Residence (住宅)
- Business (商业)
- Office (办公)
- Industry (工业)

### Q4: 如何自定义土地利用类型？
**A**: 修改 `LAND_USE_MAP` 字典，添加您数据中的具体土地利用类型：
```python
LAND_USE_MAP = {
    "住宅用地": "LandUse_Residential",
    "商业用地": "LandUse_Commercial",
    "您的类型": "LandUse_YourType",
    # 添加更多类型...
}
```

## 数据质量检查

### 运行前检查清单
- [ ] 所有shapefile文件完整（.shp, .shx, .dbf, .prj）
- [ ] 坐标系统一致（推荐WGS84）
- [ ] 必需字段存在且数据完整
- [ ] 文件路径正确且可访问
- [ ] 足够的磁盘空间存储结果

### 数据预处理建议
1. **坐标系统一**: 确保所有空间数据使用相同坐标系
2. **几何有效性**: 检查并修复无效几何
3. **属性完整性**: 确保关键字段无缺失值
4. **空间范围**: 确保数据在合理的地理范围内

## 扩展开发

### 添加新关系类型
1. 在相应的生成函数中添加逻辑
2. 更新 `relation_tasks` 列表
3. 添加参数配置
4. 更新文档说明

### 自定义实体类型
1. 定义新的ID前缀
2. 添加实体映射逻辑
3. 更新统计函数
4. 修改输出格式

## 技术支持

### 联系方式
- 开发团队: [联系信息]
- 技术文档: [文档链接]
- 问题反馈: [Issue链接]

### 贡献指南
欢迎提交Pull Request和Issue，请遵循以下规范：
1. 详细描述问题或改进建议
2. 提供测试数据和复现步骤
3. 遵循代码风格规范
4. 更新相关文档

---

**免责声明**: 本工具仅供学术研究和技术开发使用，请确保数据使用符合相关法律法规。

**版权信息**: © 2024 知识图谱生成器项目组
